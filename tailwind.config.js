/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      screens: {
        mobile: { min: "320px", max: "560px" },
      },
      // colors: {
      //   // primaryBg: "#ff743d",
      //   // secondaryBg: "#F2F2F2",
      //   // thirdBg: "#F5F5F5",
      //   // hoverBlue: "#eff4ff",
      //   // runningBtn: "#f39f4b",
      //   // grayColor: "#949494",
      // },
      // width: {
      //   100: "45rem",
      // },
      // fontFamily: {
      //   inter: ["Inter", "sans-serif"],
      // },
    },
  },
  plugins: [],
  corePlugins: {
    preflight: false,
  },
};
