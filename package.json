{"name": "allsocoalpost", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.3.6", "@fontsource/inter": "^5.2.5", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.16.0", "antd-img-crop": "^4.21.0", "antd-input-otp": "^2.1.0", "antd-style": "^3.7.1", "axios": "^1.6.8", "clsx": "^2.1.1", "dayjs": "^1.11.10", "lucide-react": "^0.479.0", "moment": "^2.30.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "react-infinite-scroll-component": "^6.1.0", "react-router-dom": "6.11.2", "react-scripts": "5.0.1", "styled-components": "^6.1.17", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && cp build/index.html build/404.html", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "tailwindcss": "^3.3.2"}}