import React, { useEffect, useState } from "react";
import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Router<PERSON>rovider } from "react-router-dom";
import { Config<PERSON><PERSON><PERSON>, Spin } from "antd";
import ALL_ROUTES from "./util/Route";
import useHttp from "./hooks/use-http";
import CONSTANTS from "./util/constant/CONSTANTS";
import { isLogin } from "./util/functions";
import theme from "./theme.json";
import { LoadingOutlined } from "@ant-design/icons";

function App() {
  const API = useHttp();
  const [userDetails, setUserDetails] = useState({});
  const [refresh, setRefresh] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!isLogin()) {
      setLoading(false); // <- if not logged in, stop loading
      return;
    }

    API.sendRequest(CONSTANTS.API.auth.getMe, (res) => {
      if (res?.token) {
        localStorage.setItem("token", res?.token);
        window.location.reload();
      }
      if (res?.data) {
        setUserDetails(res?.data);

        const isOnBillingPage = window.location.pathname === "/billing";
        const plan = res?.data?.plan;
        const planStatus = plan?.status;

        const isSubscriptionInProgress =
          planStatus === "processing" ||
          !["cancelled", "active"].includes(planStatus);

        if (
          (!isOnBillingPage && !plan) ||
          (!isOnBillingPage && plan && isSubscriptionInProgress)
        ) {
          window.location.assign("/billing");
        } else {
          setLoading(false); // safe to render app
        }
      } else {
        setLoading(false); // <- also stop loading on failed response
      }
    });
  }, [refresh]);

  const router = createBrowserRouter(
    ALL_ROUTES({
      user_details: userDetails,
      setRefresh: setRefresh,
    })
  );

  return (
    <ConfigProvider theme={theme}>
      {loading ? (
        <div className="h-screen flex justify-center items-center">
          <Spin indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />} />
        </div>
      ) : (
        <RouterProvider router={router} />
      )}
    </ConfigProvider>
  );
}

export default App;
