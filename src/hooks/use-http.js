/* eslint-disable no-prototype-builtins */
/* eslint-disable no-restricted-syntax */
import { useState, useCallback } from "react";
import axios from "axios";
import { notification } from "antd";
import Services from "../util/API/service";
import { deleteAuthDetails } from "../util/API/authStorage";

export const buildQueryString = (params) => {
  const queryParts = [];

  for (const key in params) {
    if (params.hasOwnProperty(key)) {
      const value = params[key];

      if (key.startsWith("autogenerate-mul-array-") && Array.isArray(value)) {
        const arrayKey = key.slice("autogenerate-mul-array-".length);
        value.forEach((item) => {
          queryParts.push(
            `${arrayKey}=${item}`
            // `${encodeURIComponent(arrayKey)}=${encodeURIComponent(item)}`
          );
        });
      } else {
        // Handle other cases
        queryParts.push(
          `${key}=${value}`
          // `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
        );
      }
    }
  }

  return queryParts.length > 0 ? `?${queryParts.join("&")}` : "";
};

const useHttp = () => {
  const [isLoading, setIsLoading] = useState(false);

  const sendRequest = useCallback(
    async (
      url,
      responseHandler,
      payload,
      successMessage,
      errorHandler,
      onUploadProgressCallback
    ) => {
      setIsLoading(true);
      try {
        let response;

        const axiosConfig = {
          onUploadProgress: (progressEvent) => {
            const { loaded, total } = progressEvent;
            if (onUploadProgressCallback && total) {
              onUploadProgressCallback({
                loaded,
                total,
                percentage: Math.round((loaded * 100) / total),
              });
            }
          },
        };

        switch (url.type) {
          case "POST":
            response = await Services.post(url.endpoint, payload, axiosConfig);
            break;

          case "PUT":
            response = await Services.put(url.endpoint, payload);

            break;
          case "DELETE":
            response = await Services.delete(url.endpoint);
            break;

          case "PATCH":
            response = await Services.patch(url.endpoint, payload);
            break;

          default:
            const queryParams = buildQueryString(payload);
            response = await Services.get(`${url.endpoint}${queryParams}`);
            break;
        }

        const data = await response?.data;
        if (successMessage) {
          notification.success({ message: successMessage, duration: "3" });
        }
        try {
          if (responseHandler) {
            responseHandler(data);
          }
        } catch (e) {
          console.log(e);
        }
      } catch (err) {
        // console.log(err?.response);
        // if (err?.response?.data?.status === 401 && err?.response?.data?.message === "Your account is blocked") {
        //   window.location.replace('/blockuser');
        // } else
        if (
          err?.response?.data?.status === 401 &&
          err?.response?.data?.message === "Your account is suspended"
        ) {
          window.location.replace("/suspendeduser");
        }
        if (
          err?.response?.status === 401 &&
          err?.response?.data?.status === "Permission Denied"
        ) {
          window.location.assign("/blockuser");
        }
        // else if () {

        // } else
        // if (err?.response?.data?.status === 401) {
        //   deleteAuthDetails();
        //   localStorage.removeItem("token");
        //   window.location.assign('/');
        // }
        if (
          err?.response?.data?.message === "jwt expired" ||
          err?.response?.data?.message === "Invalid token" ||
          err?.response?.data?.message === "You are not authorize person"
        ) {
          deleteAuthDetails();
          localStorage.removeItem("name");
          localStorage.removeItem("email");
          localStorage.removeItem("token");
          window.location.reload();
        }
        if (errorHandler) {
          errorHandler(err?.response?.data?.message);
        } else if (err?.response?.data?.message) {
          notification.error({
            message: err?.response?.data?.message,
            duration: "3",
          });
        } else {
          notification.error({ message: "Something Wrong Please Try again" });
        }
      }
      setIsLoading(false);
    },
    []
  );

  const sendBulkRequest = useCallback(
    async (urls, responseHandler, successMessage, errorHandler) => {
      setIsLoading(true);
      try {
        const response = await axios.all(
          urls?.map((url) => {
            switch (url?.url?.type) {
              case "POST":
                return Services.post(url?.url?.endpoint, url?.payload);

              case "PUT":
                return Services.put(url?.url?.endpoint, url?.payload);

              case "DELETE":
                return Services.delete(url?.url?.endpoint);

              case "PATCH":
                return Services.patch(url?.url?.endpoint, url?.payload);

              default:
                return Services.get(url?.endpoint);
            }
          })
        );

        if (successMessage) {
          notification.success({ message: successMessage, duration: "3" });
        }
        try {
          if (responseHandler) {
            responseHandler(response);
          }
        } catch (e) {
          console.log(e);
        }
      } catch (err) {
        // if (err?.response?.status === 401 && err?.response?.data?.status === "Permission Denied") {
        //   window.location.assign('/blockuser');
        // }
        if (
          err?.response?.data?.status === 401 &&
          err?.response?.data?.message === "Your account is blocked"
        ) {
          window.location.replace("/blockuser");
        } else if (
          err?.response?.data?.status === 401 &&
          err?.response?.data?.message === "Your account is suspended"
        ) {
          window.location.replace("/suspendeduser");
        }
        if (
          err?.response?.data?.message === "jwt expired" ||
          err?.response?.data?.message === "Invalid token" ||
          err?.response?.data?.message === "You are not authorize person"
        ) {
          deleteAuthDetails();
          localStorage.removeItem("name");
          localStorage.removeItem("email");
          localStorage.removeItem("token");
          window.location.reload();
        }
        console.log(err?.response?.data?.message);
        if (err?.response?.data?.message) {
          notification.error({
            message: err?.response?.data?.message,
            duration: "3",
          });
          if (errorHandler) {
            errorHandler(err?.response?.data?.message);
          }

          return;
        }

        // eslint-disable-next-line consistent-return
        return notification.error({
          message: "Something Wrong Please Try again",
        });
      }
      setIsLoading(false);
    },
    []
  );
  return {
    isLoading,
    sendRequest,
    sendBulkRequest,
  };
};

export default useHttp;
