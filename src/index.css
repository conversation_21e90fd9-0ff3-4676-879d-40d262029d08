@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f5f5f5;
}

@media (max-width: 560px) {
  .mobilescale {
    zoom: 0.8;
  }
  .auth-modal .ant-modal-body {
    padding: 30px !important;
  }
  .ant-tabs .ant-tabs-tab + .ant-tabs-tab {
    margin: 0 !important;
  }
  .header-res {
    zoom: 0.86;
  }
  .in-mobile {
    zoom: 0.8;
  }
}

@media (min-width: 561px) {
  .mobilescale {
    zoom: 0.9;
  }
  .header-res {
    zoom: 0.96;
  }
}

@media (min-width: 1280px) and (max-width: 1350px) {
  .macair {
    zoom: 0.78;
  }
}

.input-otp {
  justify-content: space-between !important;
  gap: 0 !important;
}

.bg-auth {
  background-image: url("/src/asset/image/Login Screen.png");
  background-size: cover;
  background-position: center;
}

input:-webkit-autofill {
  background-color: transparent !important;
  transition: background-color 5000s ease-in-out 0s;
}

.ant-input-affix-wrapper .ant-input-prefix {
  margin-inline-end: 12px !important;
}

.go-unlimited span {
  width: 100%;
}

/* Card Border */
.ant-card-custom {
  border-color: #d9dbdf !important;
}

/* Card Header Bottom Border */
.ant-card-head {
  border-bottom: 1px solid #d9dbdf !important;
}

.ant-card-custom-m-select {
  border-color: #a855f7 !important;
}

@media only screen and (min-width: 1024px) {
  .ant-menu-item-selected::before {
    width: 5px;
    position: absolute;
    top: 0.4rem;
    bottom: 0.4rem;
    left: 0px;
    background: #a855f7;
    border-radius: 0 5px 5px 0;
    content: "";
  }
}

.ant-menu-light.ant-menu-horizontal > .ant-menu-item-selected::after {
  border-bottom-width: 6px !important;
  border-radius: 10px 10px 0 0 !important;
}

.ant-menu-horizontal .ant-menu-item {
  padding: 0 16px 0 26px !important;
}

.ant-tabs-nav .ant-tabs-ink-bar {
  height: 8px !important;
  border-radius: 20px 20px 0 0 !important;
}

.write-post .ant-tabs-nav-wrap {
  justify-content: center !important;
}

.write-post .ant-tabs-tab-btn {
  margin-bottom: 13px !important;
}

.image-all .ant-checkbox-inner {
  width: 22px;
  height: 22px;
}

.ant-upload-wrapper.ant-upload-picture-card-wrapper
  .ant-upload.ant-upload-select {
  height: 100% !important;
  width: 100% !important;
}

.ant-menu-light .ant-menu-item-selected {
  color: #9333ea !important;
}

.ant-menu-light .ant-menu-item-selected svg {
  stroke: #9333ea !important;
}

.ant-menu-title-content {
  margin-inline-start: 10px !important;
}

.ant-menu-inline .ant-menu-item {
  font-weight: 500 !important;
  margin-bottom: 15px;
}

.ant-menu-light.ant-menu-horizontal > .ant-menu-item {
  display: flex;
  align-items: center;
}

.ant-select .ant-select-arrow {
  color: #828997 !important;
}

.scroll-media-modal {
  overflow-y: auto;
  max-height: calc(100vh - 250px);
  padding-right: 10px;
}

/* Custom scrollbar for horizontal scrolling */
.custom-scrollbar::-webkit-scrollbar {
  height: 6px; /* Thin scrollbar */
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1; /* Light gray track */
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #9ca3af; /* Gray thumb */
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #6b7280; /* Darker gray on hover */
}

.ant-picker .ant-picker-input > input {
  font-size: 16px !important;
}

.filter-status .ant-select-selector {
  height: 50px !important;
}

.ant-select .ant-select-clear {
  right: 10px !important;
  font-size: 18px !important;
  height: 18px !important;
  width: 18px !important;
}

.ant-menu-vertical .ant-menu-item {
  margin-block: 14px !important;
}

.ant-menu-item {
  color: #4b5563 !important;
}

.line-clamp-12 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 12;
}

.line-clamp-11 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 11;
}

.line-clamp-9 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 9;
}

.line-clamp-7 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 7;
}

.line-clamp-8 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 8;
}

.ant-select-item-option-content {
  display: flex !important;
  align-items: center !important;
}

.ant-segmented .ant-segmented-item {
  color: #a855f7 !important;
}

.ant-segmented .ant-segmented-item-selected {
  background-color: #a855f7 !important;
  color: white !important;
}

.ant-segmented .ant-segmented-thumb {
  background-color: #a855f7 !important;
}

.ant-segmented.ant-segmented-lg .ant-segmented-item-label {
  min-height: 40px !important;
  line-height: 40px !important;
  padding: 0 30px !important;
  font-size: 17px !important;
}

/* This targets the scrollbar inside Ant Design's Select dropdown */
.rc-virtual-list-holder::-webkit-scrollbar {
  width: 6px;
}

.rc-virtual-list-holder::-webkit-scrollbar-track {
  background: transparent;
}

.rc-virtual-list-holder::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 4px;
  transition: background 0.3s;
}

.rc-virtual-list-holder::-webkit-scrollbar-thumb:hover {
  background-color: #a1a1a1;
}

/* Optional: improve dropdown max height if needed */
.rc-virtual-list-holder {
  max-height: 250px;
  overflow-y: auto;
}

.custom-checkbox .ant-checkbox-inner {
  width: 20px;
  height: 20px;
}

/* Custom scrollbar */
.custom-scroll::-webkit-scrollbar {
  width: 8px;
}
.custom-scroll::-webkit-scrollbar-track {
  background: transparent;
}
.custom-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

/* Finished step circle + checkmark */
.ant-steps-item-finish .ant-steps-item-icon {
  background-color: #ec4899 !important;
  border-color: #ec4899 !important;
}

.ant-steps-item-finish .ant-steps-icon {
  color: white !important; /* checkmark color */
}

/* Current step circle */
.ant-steps-item-process .ant-steps-item-icon {
  background-color: #ec4899 !important;
  border-color: #ec4899 !important;
}

/* Current step text color */
.ant-steps-item-process .ant-steps-item-title {
  color: #ec4899 !important;
}

/* Connector line (after each completed step) */
.ant-steps-item-finish .ant-steps-item-tail::after {
  background-color: #ec4899 !important;
}

/* Connector line (active step left line) */
.ant-steps-item-process .ant-steps-item-tail::after {
  background-color: #ec4899 !important;
}

:where(.css-dev-only-do-not-override-3xztrd).ant-steps
  .ant-steps-item-finish
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title::after {
  background-color: #ec4899 !important;
}

/* Increase icon size */
.ant-steps .ant-steps-item-icon {
  width: 40px !important;
  height: 40px !important;
  font-size: 20px !important;
  line-height: 40px !important;
}

/* Optional: increase connector line thickness */
.ant-steps .ant-steps-item-tail::after {
  height: 4px !important;
}

/* Optional: make title text larger */
.ant-steps .ant-steps-item-title {
  font-size: 20px !important;
  line-height: 40px !important;
}

:where(.css-dev-only-do-not-override-3xztrd).ant-card .ant-card-body::before {
  content: none !important;
}

:where(.css-dev-only-do-not-override-3xztrd).ant-card .ant-card-body::after {
  content: none !important;
}

.ant-radio-label {
  display: flex;
  align-items: center;
}
