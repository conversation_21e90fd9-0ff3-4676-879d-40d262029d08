import React from "react";
import React<PERSON>low, { Background } from "reactflow";
import "reactflow/dist/style.css";

const Flow = ({
  initialNodes = [],
  initialEdges = [],
  onEdgeClick = () => { },
}) => {
  return (
    <ReactFlow
      nodes={initialNodes}
      edges={initialEdges}
      showFitView
      nodesConnectable={false}
      edgesUpdatable={false}
      contentEditable={false}
      minZoom={0.001}
      maxZoom={100}
      onEdgeClick={onEdgeClick}
      className="border border-gray-300"
    >
      <Background showInteractive={false} expandParent={false} />
    </ReactFlow>
  );
};

export default Flow;
