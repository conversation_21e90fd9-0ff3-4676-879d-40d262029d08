import { Breadcrumb } from "antd";
import React from "react";
import { Link } from "react-router-dom";
import { appRoot } from "../util/constant/CONSTANTS";
import { House } from "lucide-react";

const AutoBreadcrumb = ({ items = [] }) => {
  return (
    <Breadcrumb
      className=" text-base"
      items={[
        {
          title: (
            <Link
              to={appRoot}
              style={{ display: "flex", alignItems: "center", gap: "4px" }}
            >
              <House strokeWidth={1.5} size={18} color="#828997" /> Home
            </Link>
          ),
        },
        ...items,
      ]}
    />
  );
};

export default AutoBreadcrumb;
