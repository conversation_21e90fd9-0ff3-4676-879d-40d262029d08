/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>, Card, Col, Pagination, Row, Table, TreeSelect,
} from "antd";
import { CSVLink } from "react-csv";
import CONSTANTS from "../../util/constant/CONSTANTS";
import Heading from "./Heading";

function CustomTable({
  name,
  title,
  dataSource,
  onChange,
  Other = {},
  extraclass,
  filterparmas = false,
  totalRecords,
  setPageNumber,
  pageNumber,
  pageSize,
  setPageSize,
  filterList = [],
  setSelectedOption,
  selectedOption,
  exportData,
}) {
  const [currentPage, setCurrentPage] = useState(pageNumber);
  const [currentPageSize, setCurrentPageSize] = useState(pageSize);

  const [renderData, setRenderData] = useState([]);
  const [pagination, setPagination] = useState([]);

  // Set Number of Pages
  const handleChangePage = (page, pageSizeUpdate) => {
    setCurrentPage(page);
    setPageNumber(page);
    setCurrentPageSize(pageSizeUpdate);
    setPageSize(pageSizeUpdate);
    // }
  };

  const filterHandler = (value) => {
    // console.log(value);
    setSelectedOption(value);
  };

  const isDataEmpty = dataSource?.length <= 0;
  const CSVData = [];

  if (exportData?.length) {
    if (!isDataEmpty) {
      CSVData[0] = exportData?.map((el) => el[1]);
      renderData?.map((item, index) => {
        CSVData[index + 1] = exportData
          ?.map((el) => el[0])
          ?.map((val) => {
            if (item != null && val in item) return item[val];
            return "";
          });
        return 0;
      });
    }
  } else {
    CSVData[0] = CONSTANTS.TABLE[name]?.map((el) => el.title);
    renderData?.map((item, index) => {
      CSVData[index + 1] = CONSTANTS.TABLE[name]?.map(
        (el) => item[el.dataIndex],
      );
      return 0;
    });
  }
  useEffect(() => {
    if (dataSource?.length !== 0) {
      setRenderData(dataSource);
      setPagination(dataSource);
    }
  }, [dataSource, pageNumber, pageSize]);

  return (
    <Row className="my-5">
      <Card className="w-full">
        <Col
          span={24}
          style={{
            marginBlock: "15px",
          }}
          className="container-body"
        >
          <Row className="mb-5">
            <Col span={16} lg={16} xl={16} xxl={17}>
              <Heading>{title}</Heading>
            </Col>
            {filterparmas && (
              <Col span={4} lg={4} xl={4} xxl={3}>
                <div className="">
                  <TreeSelect
                    style={{
                      width: "100%",
                    }}
                    dropdownStyle={{
                      overflow: "auto",
                      maxHeight: 400,
                    }}
                    allowClear
                    // defaultValue={defaultFilterOption}
                    value={selectedOption}
                    placeholder="Please an Option"
                    treeDefaultExpandAll
                    onChange={filterHandler}
                    treeData={filterList}
                  />
                </div>
              </Col>
            )}
            <Col
              span={filterparmas ? 4 : 8}
              lg={filterparmas ? 4 : 8}
              xl={filterparmas ? 4 : 8}
              xxl={filterparmas ? 4 : 7}
            >
              <div className="mr-5">
                <CSVLink filename={`${title} Table.csv`} data={CSVData}>
                  <Button
                    className="float-right textcolor"
                    type="primary"
                    ghost
                    onClick={() => { }}
                  // {...props.ButtonDefault}
                  >
                    Export CSV
                  </Button>
                </CSVLink>
              </div>
            </Col>
          </Row>

          <Table
            rowClassName={`rows-custom ${extraclass}`}
            pagination={false}
            scroll={{ x: 800, y: 1300 }}
            {...Other}
            dataSource={pagination}
            columns={CONSTANTS.TABLE[name]}
          />
        </Col>
        <Pagination
          current={pageNumber}
          pageSize={pageSize}
          total={totalRecords}
          showSizeChanger
          onChange={handleChangePage}
          className="mt-16"
        />
      </Card>
    </Row>
  );
}
CustomTable.defaultProps = {
  onChange: (selectedRowKeys, selectedRows) => {
    console.log(
      `selectedRowKeys: ${selectedRowKeys}`,
      "selectedRows: ",
      selectedRows,
    );
  },
};
export default CustomTable;
