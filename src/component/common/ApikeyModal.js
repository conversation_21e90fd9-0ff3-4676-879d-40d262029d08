import { Button, Form, Input, Modal, Spin } from "antd";
import { KeyRound } from "lucide-react";
import React, { useEffect } from "react";
import { IoMdInformationCircleOutline } from "react-icons/io";
import { IoCloseOutline } from "react-icons/io5";
import GradientButton from "./GradientButton";
import useHttp from "../../hooks/use-http";
import { LoadingOutlined } from "@ant-design/icons";
import CONSTANTS from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import { useParams } from "react-router-dom";

const ApikeyModal = ({ openApiModal, setOpenApiModal }) => {
  const API = useHttp();
  const { teamId } = useParams();
  const [form] = Form.useForm();

  useEffect(() => {
    if (!openApiModal || !teamId) return;
    API.sendRequest(
      apiGenerator(CONSTANTS.API.openapikey.getOpenApi, { teamId }),
      (res) => {
        form.setFieldsValue({ openAPIKey: res?.key });
      }
    );
  }, [openApiModal, teamId]);

  const updateOne = (value) => {
    API.sendRequest(
      apiGenerator(CONSTANTS.API.teams.update, { teamId }),
      (res) => {
        setOpenApiModal(false);
      },
      value,
      "Key updated successfully."
    );
  };

  return (
    <Modal
      open={openApiModal}
      closeIcon={false}
      footer={null}
      centered
      onCancel={() => setOpenApiModal(false)}
      maskClosable={false}
      width={650}
      style={{ zoom: 0.9 }}
      className="md:rounded-[40px] rounded-[20px] overflow-hidden p-0 auth-modal"
      styles={{
        content: { padding: 0 },
        body: { padding: "50px" },
        mask: { backgroundColor: "rgba(0, 0, 0, 0.8)" },
      }}
    >
      <Spin
        spinning={API?.isLoading}
        indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
      >
        <div className="flex items-center justify-between">
          <div className="sm:text-3xl text-2xl font-semibold text-[#020817]">
            OpenAI API Key
          </div>
          <IoCloseOutline
            size={32}
            color="#9ca8bc"
            className="cursor-pointer mt-[6px]"
            onClick={() => {
              form.resetFields();
              setOpenApiModal(false);
            }}
          />
        </div>
        <div className="text-sm font-normal mt-1 text-[#6b7280] mb-10 pe-10">
          Securely Add Your OpenAI API Key to Unlock AI Features.
        </div>
        <Form name="team_m" onFinish={updateOne} form={form} validateTrigger="onBlur">
          <p className="font-medium text-base text-[#020817] required-field mb-2">
            API Key<span className="text-red-600 ml-1">*</span>
          </p>
          <Form.Item
            className="mb-2"
            name="openAPIKey"
            rules={[
              { required: true, message: "Please Enter API Key!" },
              {
                whitespace: true,
                message: "Input cannot be empty or spaces only",
              },
            ]}
          >
            <Input.Password
              prefix={<KeyRound strokeWidth={1.5} size={22} color="#828997" />}
              placeholder="enter api key"
              autoComplete="off"
            />
          </Form.Item>
          <div className="flex items-center gap-1 text-sm font-normal text-[#6b7280]">
            <IoMdInformationCircleOutline size={20} />
            <span>
              Your API key will be stored securely in your browser's local
              storage.
            </span>
          </div>
          <Form.Item className="mt-[50px] mb-0">
            <div className="flex gap-4">
              <GradientButton
                htmlType="submit"
                className="w-1/2 "
                buttonText={"Save API Key"}
                loading={API?.isLoading}
              />
              <Button
                className="w-1/2"
                onClick={() => {
                  setOpenApiModal(false);
                }}
              >
                Cancel
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default ApikeyModal;
