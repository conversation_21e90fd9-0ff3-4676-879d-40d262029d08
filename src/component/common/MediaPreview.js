import { <PERSON><PERSON>, Modal } from "antd";
import { ArrowDownTo<PERSON><PERSON>, Dot } from "lucide-react";
import React from "react";
import { IoCloseOutline } from "react-icons/io5";

const MediaPreview = ({ open, setOpen }) => {
  if (!open) return null;

  const isVideo = open?.type === "video";

  const handleMedia = () => {
    if (!open?.url) return;

    if (open.type === "image") {
      window.open(open.url, "_blank");
    } else if (open.type === "video") {
      fetch(open.url)
        .then((response) => response.blob())
        .then((blob) => {
          const url = URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = "video.mp4";
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        })
        .catch((error) => console.log("Error downloading video:", error));
    } else {
      console.warn("Unsupported media type:", open?.type);
    }
  };

  return (
    <Modal
      open={open}
      closeIcon={false}
      footer={null}
      centered
      onCancel={() => setOpen(null)}
      maskClosable={true}
      width={650}
      style={{ zoom: 0.9 }}
      className="md:rounded-[40px] rounded-[20px] overflow-hidden p-0 auth-modal"
      styles={{
        content: { padding: 0 },
        body: { padding: "50px" },
        mask: { backgroundColor: "rgba(0, 0, 0, 0.8)" },
      }}
    >
      <div className="flex items-center justify-between">
        <div className="sm:text-3xl text-2xl font-semibold text-[#020817] truncate">
          {open?.name}
        </div>
        <div className="w-9">
          <IoCloseOutline
            size={32}
            color="#9ca8bc"
            className="cursor-pointer mt-[6px]"
            onClick={() => setOpen(null)}
          />
        </div>
      </div>
      <div className="text-sm font-normal text-[#6b7280] mb-[30px] mt-1 pe-10 flex items-center">
        {isVideo ? "VIDEO" : "IMAGE"} <Dot strokeWidth={1.5} />
        {(open?.size / 1024 / 1024).toFixed(2)} MB <Dot strokeWidth={1.5} />
        {open?.width && open?.height && `${open.width} x ${open.height}`}
      </div>
      <div className="relative">
        {isVideo ? (
          <video
            src={open?.url}
            controls
            className="object-cover w-full rounded-[20px] max-h-[500px]"
          />
        ) : (
          <img
            src={open?.url}
            alt="media-preview"
            className="object-cover w-full rounded-[20px] max-h-[500px]"
          />
        )}
        {open?.width && open?.height && (
          <Avatar
            icon={<ArrowDownToLine strokeWidth={1.5} size={24} />}
            className="bg-[#00000080] absolute right-5 top-5 cursor-pointer"
            shape="square"
            size={40}
            onClick={handleMedia}
          />
        )}
      </div>
    </Modal>
  );
};

export default MediaPreview;
