import { But<PERSON>, Card, notification, Popconfirm, Spin, Upload } from "antd";
import { Eye, Image, Info, Play, Plus, SquarePlay, Trash2 } from "lucide-react";
import React, { useState } from "react";
import MediaPreview from "./MediaPreview";
import CONSTANTS from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import useHttp from "../../hooks/use-http";
import { useParams } from "react-router-dom";
import { LoadingOutlined } from "@ant-design/icons";
import GradientButton from "./GradientButton";
import { useWritePost } from "../../page/app/dashboard/WritePostContext";
import postValidationRules from "../../util/image";

const MediaList = ({
  selectedMedia,
  setSelectedMedia,
  onSelect,
  card,
  onCancel,
}) => {
  const API = useHttp();
  const { teamId } = useParams();
  const { fileList, setFileList } = useWritePost();
  const [open, setOpen] = useState(null);

  const handleUploadChange = ({ file }) => {
    if (!teamId || !file) return;

    const formData = new FormData();
    formData.append("file", file);

    API.sendRequest(
      apiGenerator(CONSTANTS.API.media.add, {}, `?teamId=${teamId}`),
      (res) => {
        if (res?.data) {
          setFileList((prev) => ({ ...prev, [res?.data?.id]: res.data }));
        }
      },
      formData,
      "Media uploaded successfully."
    );
  };

  const handleDelete = (mId) => {
    if (!mId) return;

    API.sendRequest(
      apiGenerator(CONSTANTS.API.media.delete, { mId }),
      (res) => {
        setFileList((prev) => {
          const updatedList = { ...prev };
          delete updatedList[mId]; // Remove the deleted media from the object
          return updatedList;
        });

        setSelectedMedia((prevSelected) =>
          prevSelected?.filter((file) => file.id !== mId)
        );
      },
      {},
      "Media deleted successfully."
    );
  };

  const toggleMediaSelection = (file) => {
    if (!card) {
      setSelectedMedia((prevSelected) =>
        prevSelected.includes(file.id)
          ? prevSelected.filter((id) => id !== file.id)
          : [...prevSelected, file.id]
      );
      return;
    }

    const mediaRules = postValidationRules[card]?.media;
    if (!mediaRules) return;

    setSelectedMedia((prevSelected) => {
      const selectedFiles = [...prevSelected];
      const isAlreadySelected = selectedFiles.includes(file.id);
      const selectedVideos = selectedFiles.filter((id) =>
        Object.values(fileList)?.find(
          (media) => media.id === id && media.type === "video"
        )
      );
      const selectedImages = selectedFiles.filter((id) =>
        Object.values(fileList)?.find(
          (media) => media.id === id && media.type === "image"
        )
      );

      if (isAlreadySelected) {
        return selectedFiles.filter((id) => id !== file.id);
      }

      if (card === "YOUTUBE" && file.type !== "video") {
        notification.warning({
          message: "Media Selection Warning",
          description: "YouTube only supports video uploads.",
        });
        return prevSelected;
      }
      if (card === "YOUTUBE" && selectedVideos.length >= 1) {
        notification.warning({
          message: "Media Selection Warning",
          description: "You can only upload 1 video for YouTube.",
        });
        return prevSelected;
      }

      if (typeof mediaRules.allowed === "object") {
        if (file.type === "video") {
          if (
            selectedImages.length > 0 ||
            selectedVideos.length >= mediaRules.allowed.video
          ) {
            notification.warning({
              message: "Media Selection Warning",
              description: mediaRules.mediaValidationMessage,
            });
            return prevSelected;
          }
        } else if (file.type === "image") {
          if (
            selectedVideos.length > 0 ||
            selectedImages.length >= mediaRules.allowed.image
          ) {
            notification.warning({
              message: "Media Selection Warning",
              description: mediaRules.mediaValidationMessage,
            });
            return prevSelected;
          }
        }
      } else {
        if (selectedFiles.length >= mediaRules.allowed) {
          notification.warning({
            message: "Media Selection Warning",
            description: mediaRules.mediaValidationMessage,
          });
          return prevSelected;
        }
      }

      return [...selectedFiles, file.id];
    });
  };

  return (
    <>
      <Spin
        spinning={API?.isLoading}
        indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
      >
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-3 md:gap-5 mt-[30px] min-h-28 scroll-media-modal">
          <Upload
            listType="picture-card"
            multiple={true}
            customRequest={({ file }) => handleUploadChange({ file })}
            showUploadList={false}
            beforeUpload={(file) => {
              const imageTypes = ["image/jpeg", "image/jpg", "image/png"];
              const videoTypes = ["video/mp4"];

              const isImage = imageTypes.includes(file.type);
              const isVideo = videoTypes.includes(file.type);

              if (!isImage && !isVideo) {
                notification.info({
                  message:
                    "Only JPG, JPEG, PNG images and MP4 videos are allowed.",
                  duration: 3,
                });
                return false;
              }

              if (isImage) {
                const isUnderLimit = file.size / 1024 / 1024 <= 25; // Convert bytes to MB
                if (!isUnderLimit) {
                  notification.info({
                    message: "Image file size must be less than 25MB.",
                    duration: 3,
                  });
                  return false;
                }
              }

              if (isVideo) {
                const isUnderLimit = file.size / 1024 / 1024 <= 1024; // 1GB
                if (!isUnderLimit) {
                  notification.info({
                    message: "Video file size must be less than 1GB.",
                    duration: 3,
                  });
                  return false;
                }
              }

              return true;
            }}
          >
            <div className="flex flex-col items-center justify-center">
              <Plus strokeWidth={1.5} color="#6b7280" size={20} />
              <div className="text-sm font-normal">Upload</div>
            </div>
          </Upload>

          {Object.values(fileList).map((file) => (
            <Card
              key={file?.id}
              styles={{
                body: {
                  padding: 7,
                },
              }}
              className={`${
                selectedMedia.includes(file.id)
                  ? "ant-card-custom-m-select bg-[#faf5ff]"
                  : "ant-card-custom"
              }`}
              onClick={() => toggleMediaSelection(file)}
            >
              <div className="relative group w-full h-24 sm:h-28 md:h-24">
                <img
                  src={file?.thumbnailUrl}
                  alt={"Url"}
                  className="object-cover w-full h-24 sm:h-28 md:h-24 rounded-md"
                />
                {file?.type !== "video" ? (
                  <Image
                    strokeWidth={1.5}
                    size={18}
                    color="#fff"
                    className="top-1 right-1 absolute bg-black/50 rounded-[4px] p-[2px]"
                  />
                ) : (
                  <Play
                    strokeWidth={1.5}
                    color="#fff"
                    size={14}
                    className="top-1 right-1 absolute bg-black/50 rounded-[4px] p-[4px]"
                  />
                )}
                <div className="absolute w-full h-24 sm:h-28 md:h-24 top-0 flex items-center justify-center gap-2 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-md">
                  <Eye
                    strokeWidth={1.5}
                    size={20}
                    color="#fff"
                    className="cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      setOpen(file);
                    }}
                  />
                  <Popconfirm
                    title="Delete image"
                    description="Are you sure you want to delete this image?"
                    icon={
                      <Info
                        strokeWidth={1.5}
                        size={16}
                        color="red"
                        className="mt-[2px] me-1"
                      />
                    }
                    okText="Yes"
                    cancelText="No"
                    onConfirm={(e) => {
                      e.stopPropagation();
                      handleDelete(file?.id);
                    }}
                    onCancel={(e) => e.stopPropagation()}
                  >
                    <Trash2
                      strokeWidth={1.5}
                      size={20}
                      color="#fff"
                      className="cursor-pointer"
                      onClick={(e) => e.stopPropagation()}
                    />
                  </Popconfirm>
                </div>
              </div>
            </Card>
          ))}
        </div>
        <div className="flex gap-4 mt-[50px]">
          <GradientButton
            htmlType="submit"
            className="w-1/2 "
            buttonText="Select"
            onClick={onSelect}
          />
          <Button className="w-1/2" onClick={onCancel}>
            Cancel
          </Button>
        </div>
      </Spin>
      <MediaPreview open={open} setOpen={setOpen} />
    </>
  );
};

export default MediaList;
