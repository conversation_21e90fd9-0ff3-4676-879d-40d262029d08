import {
  Button, Col, Form, Row,
} from "antd";
import React from "react";
import FormFields from "./FormFields";

function FormWithButton({
  menu,
  formData,
  formFields = [],
  onCreate,
  name = "Create",
  threeField = false,
  inline = true,
}) {
  const [form] = Form.useForm();
  return (
    <Row className={`${!inline ? "flex justify-center" : ""}`}>
      <Col span={inline ? 18 : 24} className="">
        <FormFields
          formData={formData}
          menu={menu}
          formFields={formFields}
          form={form}
          normal
          threeField={threeField}
        />
      </Col>

      <Col span={6} className="">
        <Button
        className="textcolor"
          onClick={() => {
            form
              .validateFields()
              .then((values) => {
                form.resetFields();
                onCreate(values);
              })
              .catch((info) => {
                console.error("Validate Failed:", info);
              });
          }}
          type="primary"
          ghost
        >
          {name}
        </Button>
      </Col>
    </Row>
  );
}

export default FormWithButton;
