import React from "react";
import { Button } from "antd";
import { createStyles } from "antd-style";

const useStyle = createStyles(({ prefixCls, css }) => ({
  linearGradientButton: css`
    &.${prefixCls}-btn-primary:not([disabled]):not(
        .${prefixCls}-btn-dangerous
      ) {
      > span {
        position: relative;
      }

      &::before {
        content: "";
        background: linear-gradient(135deg, #a855f7, #ec4899);
        position: absolute;
        inset: -1px;
        opacity: 1;
        transition: all 0.3s;
        border-radius: inherit;
      }

      &:hover::before {
        background: linear-gradient(135deg, #ec4899, #a855f7);
      }
    }
  `,
}));

const GradientButton = ({
  icon = null,
  className = "",
  buttonText = "Submit",
  onClick = () => {},
  htmlType = "button",
  loading = false,
  disabled=false,
  child = null,
  shape = "",
  size = "",
}) => {
  const { styles } = useStyle();

  return (
    <Button
      type="primary"
      htmlType={htmlType}
      loading={loading}
      className={`${styles.linearGradientButton} ${className}`}
      icon={icon}
      onClick={onClick}
      disabled={disabled}
      shape={shape}
      size={size}
    >
      {child ? child : buttonText}
    </Button>
  );
};

export default GradientButton;
