import { Form, Modal, Select, Spin } from "antd";
import React, { useEffect, useState } from "react";
import { IoMdInformationCircleOutline } from "react-icons/io";
import GradientButton from "./GradientButton";
import useHttp from "../../hooks/use-http";
import { LoadingOutlined } from "@ant-design/icons";
import CONSTANTS from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import { useParams } from "react-router-dom";
import { ChevronDown } from "lucide-react";

const ChnanalModal = ({
  openChannelModal,
  platform,
  channelOpt,
  onSubmit,
  API,
}) => {
  const APICo = useHttp();
  const { teamId } = useParams();
  const [form] = Form.useForm();
  const [option, setOption] = useState([]);

  useEffect(() => {
    if (platform && channelOpt) {
      const getOptionArray =
        channelOpt.find((item) => item[platform?.toUpperCase()])?.[
          platform?.toUpperCase()
        ] || [];

      const optionsList = getOptionArray.map((item) => ({
        label: platform === "facebook" ? item.name : item.username,
        value: item.id,
        username: item.name,
      }));
      setOption(optionsList);
    }
  }, [platform, channelOpt]);

  const updateOne = (value) => {
    const selectedChannel = option?.find((cv) => cv?.value === value?.channel);
    const payload = {
      teamId: teamId,
      type: platform?.toUpperCase(),
      id: value?.channel,
      channel: selectedChannel?.label,
    };
    APICo.sendRequest(
      apiGenerator(CONSTANTS.API.connectMedia.setChannel),
      (res) => {
        onSubmit();
      },
      payload,
      "Channel updated successfully."
    );
  };

  return (
    <Modal
      open={openChannelModal}
      closeIcon={false}
      footer={null}
      centered
      maskClosable={false}
      width={500}
      style={{ zoom: 0.9 }}
      className="md:rounded-[40px] rounded-[20px] overflow-hidden p-0 auth-modal"
      styles={{
        content: { padding: 0 },
        body: { padding: "50px" },
        mask: { backgroundColor: "rgba(0, 0, 0, 0.8)" },
      }}
    >
      <Spin
        spinning={API?.isLoading}
        indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
      >
        <div className="flex items-center justify-between">
          <div className="sm:text-3xl text-2xl font-semibold text-[#020817]">
            {platform === "youtube"
              ? "Select YouTube Channel"
              : `Select ${
                  platform?.charAt(0).toUpperCase() + platform?.slice(1)
                } Page`}
          </div>
        </div>
        <div className="text-sm font-normal mt-1 text-[#6b7280] mb-10 pe-10">
          {platform === "youtube"
            ? "Select a channel to connect to this account"
            : "Select a page to connect to this account"}
        </div>
        <Form
          name="channel_form"
          onFinish={updateOne}
          form={form}
          validateTrigger="onBlur"
        >
          <p className="font-medium text-base text-[#020817] required-field mb-2">
            Channel<span className="text-red-600 ml-1">*</span>
          </p>
          <Form.Item
            className="mb-2"
            name="channel"
            rules={[{ required: true, message: "Please select a channel!" }]}
          >
            <Select
              placeholder="Select channel"
              optionFilterProp="label"
              options={option}
              suffixIcon={
                <ChevronDown size={22} strokeWidth={1.5} color="#828997" />
              }
            />
          </Form.Item>

          <div className="flex items-center gap-1 text-sm font-normal text-[#6b7280]">
            <IoMdInformationCircleOutline size={20} />
            <span>
              To create posts, select a connected {platform}{" "}
              {platform === "youtube" ? "channel" : "page"}.
            </span>
          </div>

          <Form.Item className="mt-[50px] mb-0">
            <GradientButton
              htmlType="submit"
              className="w-full"
              buttonText={"Submit"}
              loading={APICo?.isLoading}
            />
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default ChnanalModal;
