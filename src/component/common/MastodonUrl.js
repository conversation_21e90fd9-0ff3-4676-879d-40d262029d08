import { But<PERSON>, Form, Input, Modal, Spin } from "antd";
import { Link } from "lucide-react";
import React from "react";
import { IoCloseOutline } from "react-icons/io5";
import GradientButton from "./GradientButton";
import useHttp from "../../hooks/use-http";
import { LoadingOutlined } from "@ant-design/icons";
import CONSTANTS from "../../util/constant/CONSTANTS";
import { useParams } from "react-router-dom";

const MastodonUrl = ({ openUrlModal, setOpenUrlModal }) => {
  const API = useHttp();
  const { teamId } = useParams();
  const [form] = Form.useForm();

  const connectMedia = (value) => {
    if (teamId)
      API?.sendRequest(
        CONSTANTS.API.connectMedia.connectOne,
        (res) => {
          if (res?.url) window.open(res?.url, "_blank");
        },
        {
          teamId: teamId,
          type: "MASTODON",
          ...value,
          redirectUrl: window.location.href,
        }
      );
  };

  return (
    <Modal
      open={openUrlModal}
      closeIcon={false}
      footer={null}
      centered
      onCancel={() => setOpenUrlModal(false)}
      maskClosable={false}
      width={650}
      style={{ zoom: 0.9 }}
      className="md:rounded-[40px] rounded-[20px] overflow-hidden p-0 auth-modal"
      styles={{
        content: { padding: 0 },
        body: { padding: "50px" },
        mask: { backgroundColor: "rgba(0, 0, 0, 0.8)" },
      }}
    >
      <Spin
        spinning={API?.isLoading}
        indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
      >
        <div className="flex items-center justify-between">
          <div className="sm:text-3xl text-2xl font-semibold text-[#020817]">
            Mastodon Server Url
          </div>
          <IoCloseOutline
            size={32}
            color="#9ca8bc"
            className="cursor-pointer mt-[6px]"
            onClick={() => {
              form.resetFields();
              setOpenUrlModal(false);
            }}
          />
        </div>
        <div className="text-sm font-normal mt-1 text-[#6b7280] mb-10 pe-10">
          Add your Mastodon server url.
        </div>
        <Form
          name="team_m"
          onFinish={connectMedia}
          form={form}
          validateTrigger="onBlur"
          initialValues={{
            serverUrl: "https://mastodon.social",
          }}
        >
          <p className="font-medium text-base text-[#020817] required-field mb-2">
            Server Url<span className="text-red-600 ml-1">*</span>
          </p>
          <Form.Item
            className="mb-2"
            name="serverUrl"
            rules={[
              { required: true, message: "Please enter server URL!" },
              {
                whitespace: true,
                message: "Input cannot be empty or spaces only",
              },
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve(); // already handled by required
                  try {
                    new URL(value);
                    return Promise.resolve();
                  } catch {
                    return Promise.reject(
                      new Error("Please enter a valid URL!")
                    );
                  }
                },
              },
            ]}
          >
            <Input
              prefix={<Link strokeWidth={1.5} size={22} color="#828997" />}
              placeholder="enter server URL"
              autoComplete="off"
              type="url"
            />
          </Form.Item>
          {/* <div className="flex items-center gap-1 text-sm font-normal text-[#6b7280]">
            <IoMdInformationCircleOutline size={20} />
            <span>
              Your API key will be stored securely in your browser's local
              storage.
            </span>
          </div> */}
          <Form.Item className="mt-[50px] mb-0">
            <div className="flex gap-4">
              <GradientButton
                htmlType="submit"
                className="w-1/2 "
                buttonText={"Save"}
                loading={API?.isLoading}
              />
              <Button
                className="w-1/2"
                onClick={() => {
                  form.resetFields();
                  setOpenUrlModal(false);
                }}
              >
                Cancel
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default MastodonUrl;
