import { Spin } from "antd";
import React, { memo, useEffect, useState } from "react";

const TimeoutOTP = ({ handleResendOTP }) => {
  const [canResend, setCanResend] = useState(false);
  const [resend, setResend] = useState(false);
  const [leftTime, setLeftTime] = useState(() => {
    return parseInt(localStorage.getItem("time")) || 120;
  });

  useEffect(() => {
    if (leftTime > 0) {
      const interval = setInterval(() => {
        setLeftTime((prev) => {
          const newTime = prev - 1;
          localStorage.setItem("time", newTime);
          return newTime;
        });
      }, 1000);

      return () => clearInterval(interval);
    } else {
      setCanResend(true);
      localStorage.removeItem("time");
    }
  }, [leftTime]);

  const handleResend = () => {
    if (!canResend) return;
    handleResendOTP();
    setResend(true);
  };

  // Format timer as MM:SS
  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  return (
    <span className="text-[#788094] text-sm">
      {canResend ? (
        resend ? (
          <Spin className="ms-2" size="small" />
        ) : (
          <span
            onClick={handleResend}
            className="cursor-pointer text-[#a855f7] font-medium"
          >
            Resend verification code
          </span>
        )
      ) : (
        `Resend after ${formatTime(leftTime)}`
      )}
    </span>
  );
};

export default memo(TimeoutOTP);
