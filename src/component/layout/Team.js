import { <PERSON>, Image, Popconfirm, Tag } from "antd";
import { BookText, FilePenLine, Globe, Info, Plus, Trash2 } from "lucide-react";
import React from "react";
import { useNavigate } from "react-router-dom";
import CONSTANTS from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";

const Team = ({ teamD, onEdit, onDelete, API, isSpecialUser }) => {
  const navigate = useNavigate();

  const connectAllMedia = (e) => {
    e.stopPropagation();
    if (!teamD?.id) return;
    API.sendRequest(
      apiGenerator(
        CONSTANTS.API.connectMedia.connectAll,
        {},
        `?teamId=${teamD?.id}`
      ),
      (res) => {
        if (res?.url) window.open(res?.url, "_blank");
      }
    );
  };

  return (
    <Card
      className="rounded-[20px] ant-card-custom cursor-pointer"
      styles={{ body: { padding: 20 } }}
      onClick={() => navigate(`/team/${teamD?.id}`)}
    >
      <div className="flex justify-between items-center ">
        <div className="text-lg font-semibold line-clamp-1">{teamD?.name}</div>
        <div className="flex items-center gap-2">
          <FilePenLine
            color="#828997"
            onClick={(e) => {
              e.stopPropagation();
              onEdit();
            }}
            size={24}
            strokeWidth={1.5}
            className="cursor-pointer"
          />
          <Popconfirm
            title={"Delete the team"}
            description={"Are you sure you want to delete this team?"}
            icon={
              <Info
                strokeWidth={1.5}
                size={16}
                color="red"
                className="mt-[2px] me-1"
              />
            }
            onConfirm={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            onCancel={(e) => e.stopPropagation()}
            okText={"Yes"}
            cancelText={"No"}
          >
            <Trash2
              color="#fb3748"
              size={24}
              strokeWidth={1.5}
              className="cursor-pointer"
              onClick={(e) => e.stopPropagation()}
            />
          </Popconfirm>
        </div>
      </div>
      <div className="flex mt-5 gap-2 h-[24px] items-center">
        {!!teamD?.website && (
          <>
            <div className="w-7 flex items-center">
              <Globe color="#828997" size={24} strokeWidth={1.5} />
            </div>
            <div className="line-clamp-1 text-base text-[#5b616d]">
              {teamD?.website}
            </div>
          </>
        )}
      </div>
      <div className="flex mt-5 gap-[10px] h-12">
        {teamD?.businessInfo && (
          <>
            <div className="w-7 flex">
              <BookText color="#828997" size={24} strokeWidth={1.5} />
            </div>
            <div className="line-clamp-2 text-base text-[#5b616d]">
              {teamD?.businessInfo}
            </div>
          </>
        )}
      </div>
      <div className="mt-5 flex justify-between items-center">
        <div className="flex gap-[10px]" onClick={(e) => connectAllMedia(e)}>
          <Plus strokeWidth={1.5} size={22} color="#a855f7" />
          <div className="font-medium text-base text-[#a855f7]">
            Connect Social Media
          </div>
        </div>
        {isSpecialUser && teamD?.state && teamD?.city && teamD?.phoneNumber && (
          <Tag color="green" className="text-sm  py-[2px] px-2">
            Auto Post Ready
          </Tag>
        )}
      </div>
    </Card>
  );
};

export default Team;
