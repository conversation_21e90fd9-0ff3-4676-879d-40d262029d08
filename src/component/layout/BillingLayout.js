import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Avatar,
  Card,
  Col,
  Image,
  Layout,
  Row,
  Segmented,
  Space,
  Spin,
  Typography,
} from "antd";
import RightDrawer from "./Component/right-drawer";
import CONSTANTS, { appRoot, loginRoot } from "../../util/constant/CONSTANTS";
import { getAuthToken } from "../../util/API/authStorage";
import logo from "../../asset/logos/icon.svg";
import useHttp from "../../hooks/use-http";
import { PricingCard } from "./Component/PlanCard";
import {
  basicPlan,
  bestPlan,
  boostPlan,
  filplain,
  socialMediaIcons,
} from "../../util/image";
import UserAddress from "./Component/UserAddress";
import GradientButton from "../common/GradientButton";
import moment from "moment";
import { CircleCheck } from "lucide-react";
import { LoadingOutlined } from "@ant-design/icons";

const { Header, Content } = Layout;

const BillingLayout = (props) => {
  const API = useHttp();
  const { user_details } = props;
  const subscriptionPlan = props?.user_details?.plan;
  const navigate = useNavigate();
  const [visible, setVisible] = useState(false);
  const [billingPeriod, setBillingPeriod] = useState("Month");
  const [plans, setPlans] = useState({});
  const [options, setOptions] = useState({});
  const [open, setOpen] = useState(null);

  const handleToggle = (value) => {
    setBillingPeriod(value);
  };

  const isFutureOrToday = (dateString) => {
    return moment(dateString)
      .startOf("day")
      .isSameOrAfter(moment().startOf("day"));
  };

  const getPlanList = () => {
    // if (
    //   !subscriptionPlan ||
    //   ["cancelled", "active"].includes(subscriptionPlan?.status)
    // )
    if (user_details) {
      API.sendRequest(CONSTANTS.API.payment.getPlan, (res) => {
        let plans = res?.data?.rows || [];

        const tierOrder = ["Lite", "Pro", "Ultra Boost"];

        if (
          subscriptionPlan?.name &&
          subscriptionPlan?.payment_frequency_interval &&
          isFutureOrToday(subscriptionPlan?.next_billing_date)
        ) {
          const currentTierIndex = tierOrder.indexOf(subscriptionPlan.name);
          const currentInterval = subscriptionPlan.payment_frequency_interval;

          plans = plans.filter((plan) => {
            const planTierIndex = tierOrder.indexOf(plan.name);

            // Exclude lower tier plans
            if (planTierIndex < currentTierIndex) return false;

            // Exclude same tier and shorter interval (e.g., same tier but monthly when current is yearly)
            if (
              planTierIndex === currentTierIndex &&
              plan.payment_frequency_interval !== currentInterval
            ) {
              return plan.payment_frequency_interval === "Year"; // Keep only if it's a longer plan
            }

            // Exclude exact same plan
            if (
              planTierIndex === currentTierIndex &&
              plan.payment_frequency_interval === currentInterval
            ) {
              return false;
            }

            // Otherwise, it's a higher plan — keep it
            return true;
          });
        }

        const groupedByFrequency = plans.reduce((acc, plan) => {
          const interval = plan.payment_frequency_interval;
          if (!acc[interval]) {
            acc[interval] = [];
          }
          acc[interval].push(plan);
          return acc;
        }, {});

        const prepareOptions = Object.keys(groupedByFrequency).map(
          (interval) => ({
            label:
              interval === "Month"
                ? "Monthly"
                : interval === "Year"
                ? "Yearly"
                : interval,
            value: interval,
          })
        );
        setBillingPeriod(prepareOptions?.[0]?.value);

        setOptions(prepareOptions);

        setPlans(groupedByFrequency);
      });
    }
  };

  const getBackDate = (date, type = "Month", amount = 1) => {
    const formattedDate = moment(date)
      .subtract(amount, type?.toLowerCase())
      .format("DD/MM/YYYY");
    return formattedDate;
  };

  const formatToLocalDate = (dateString) =>
    moment(dateString).local().format("DD/MM/YYYY");

  const onManageBilling = () => {
    API.sendRequest(CONSTANTS.API.payment.manageBilling, (res) => {
      window.open(res?.data?.link);
    });
  };

  useEffect(() => {
    const isLogin = getAuthToken() !== undefined && getAuthToken() !== null;
    if (!isLogin) {
      navigate(loginRoot);
      return;
    }
    getPlanList();
  }, [user_details]);

  return (
    <>
      <Layout
        hasSider
        style={{
          minHeight: "100vh",
          zoom: 0.9,
        }}
        className="site-layout w-full"
      >
        <Layout>
          <Header
            className="px-0 top-0 sticky z-10 h-[70px]"
            style={{
              backgroundColor: "#FFFFFF",
            }}
          >
            <Row
              align="middle"
              justify="space-between"
              className="mx-5 md:mx-24"
            >
              <Col span={10} md={6} className="center flex">
                <Image
                  style={{
                    cursor: "pointer",
                    objectFit: "contain",
                  }}
                  className="!w-44 md:!w-48"
                  preview={false}
                  src={logo}
                  onClick={() => {
                    subscriptionPlan &&
                      ["cancelled", "active"].includes(
                        subscriptionPlan?.status
                      ) &&
                      navigate(`${appRoot}`);
                  }}
                />
              </Col>

              <Col
                span={14}
                md={10}
                style={{
                  display: "flex",
                  justifyContent: "end",
                  alignItems: "center",
                }}
              >
                <Avatar
                  size={36}
                  className="bg-[#374151] shadow-lg cursor-pointer"
                  shape="circle"
                  onClick={() => {
                    setVisible(true);
                  }}
                >
                  <div className="font-semibold text-sm">
                    {user_details?.name?.charAt(0).toUpperCase()}
                  </div>
                </Avatar>
              </Col>
            </Row>
          </Header>
          <Content className="sm:py-[30px] sm:px-[90px] p-[20px]">
            {/* {user_details &&
              (!["cancelled", "active"].includes(subscriptionPlan?.status) ? ( */}
            <>
              {user_details &&
                ["cancelled", "active"].includes(subscriptionPlan?.status) && (
                  <Card
                    className="w-full rounded-[30px]"
                    styles={{ body: { padding: 30 } }}
                  >
                    <Row className="w-full">
                      <div className="sm:text-3xl text-2xl font-semibold sm:mb-8 mb-6">
                        Manage Your Plan
                      </div>
                      <div className="text-xl font-normal w-full">
                        ⏳ Stay Covered! Your subscription runs from{" "}
                        <span className="font-semibold text-[#ec4899]">
                          {getBackDate(
                            subscriptionPlan?.next_billing_date,
                            subscriptionPlan?.payment_frequency_interval,
                            subscriptionPlan?.payment_frequency_count
                          )}
                        </span>{" "}
                        to{" "}
                        <span className="font-semibold text-[#ec4899]">
                          {formatToLocalDate(
                            subscriptionPlan?.next_billing_date
                          )}
                        </span>
                        . Renew on time to keep enjoying seamless access! ✨
                      </div>
                      <Row className="w-full">
                        <Col
                          span={24}
                          className="mb-[50px] mt-[30px]"
                          xs={24}
                          sm={24}
                          md={18}
                          lg={12}
                          xl={10}
                          xxl={8}
                        >
                          <Card
                            className="rounded-[30px] ant-card-custom"
                            styles={{ body: { padding: 30 } }}
                          >
                            <div className="flex justify-between items-center flex-col sm:flex-row">
                              <div className="flex items-center gap-5 self-start">
                                <Avatar
                                  src={
                                    subscriptionPlan?.name === "Lite"
                                      ? basicPlan
                                      : subscriptionPlan?.name === "Pro"
                                      ? bestPlan
                                      : subscriptionPlan?.name === "Ultra Boost"
                                      ? boostPlan
                                      : basicPlan
                                  }
                                  size={60}
                                />
                                <div className="text-[#ec4899] text-xl font-medium">
                                  {subscriptionPlan?.name}
                                </div>
                              </div>
                              <div className="self-start sm:self-center mt-5 sm:mt-0">
                                <span className="sm:text-4xl text-2xl font-medium me-2">
                                  ${subscriptionPlan?.price / 100}
                                </span>
                                <span className="text-base font-normal text-[#6b7280]">
                                  /
                                  {subscriptionPlan?.payment_frequency_interval?.toLowerCase()}
                                </span>
                              </div>
                            </div>

                            <div className="flex flex-col gap-5 max-sm:gap-[15px] mt-[25px]">
                              <div className="flex items-center gap-2.5 text-base text-gray-600 max-sm:text-sm">
                                <CircleCheck color={"#EC4899"} size={20} />
                                <div>
                                  {subscriptionPlan?.isTeamUnlimited
                                    ? "Unlimited"
                                    : subscriptionPlan?.teamLimit}{" "}
                                  {subscriptionPlan?.name === "Lite"
                                    ? "Team"
                                    : "Teams"}
                                </div>
                              </div>
                              <div className="flex items-center gap-2.5 text-base text-gray-600 max-sm:text-sm">
                                <CircleCheck color={"#EC4899"} size={20} />
                                <div>
                                  {subscriptionPlan?.isPostUnlimited
                                    ? "Unlimited"
                                    : subscriptionPlan?.postLimit}{" "}
                                  Posts
                                </div>
                              </div>
                            </div>
                          </Card>
                        </Col>
                      </Row>
                      <Col
                        span={8}
                        xs={24}
                        sm={12}
                        md={12}
                        lg={10}
                        xl={7}
                        xxl={6}
                      >
                        <GradientButton
                          className="w-full"
                          buttonText="Manage Billing"
                          onClick={onManageBilling}
                          loading={API?.isLoading}
                        />
                      </Col>
                    </Row>
                  </Card>
                )}
              {!!Object.keys(plans || {})?.length && (
                <>
                  <div className="text-2xl lg:text-4xl font-semibold sm:text-3xl mt-6 mb-[36px] sm:mt-10 sm:mb-[50px] text-center">
                    Choose The Right Plan For Seamless Posting
                  </div>
                  <div className="text-center sm:mb-12 mb-9">
                    <Segmented
                      value={billingPeriod}
                      options={options}
                      onChange={handleToggle}
                      size="large"
                      className="border-solid border-2 border-[#a855f7] rounded-lg !bg-transparent"
                      style={{
                        padding: "5px",
                      }}
                    />
                  </div>
                  <Spin
                    spinning={API.isLoading}
                    indicator={
                      <LoadingOutlined style={{ fontSize: 48 }} spin />
                    }
                  >
                    <div className="flex flex-wrap justify-center gap-[30px]">
                      {plans?.[billingPeriod]?.map((plan, index) => (
                        <PricingCard
                          key={index}
                          plan={plan}
                          onUpgrade={() => setOpen(plan?.id)}
                        />
                      ))}
                    </div>
                  </Spin>
                  <div className="my-[40px] max-md:flex-col">
                    <Space
                      direction="horizontal"
                      align="center"
                      className="flex flex-wrap items-center md:justify-center gap-[15px] max-md:justify-center max-sm:gap-2.5"
                    >
                      <Typography.Text className="text-xl text-gray-500 max-sm:text-base font-light">
                        Post to:
                      </Typography.Text>
                      <Space
                        size={20}
                        className="flex flex-wrap items-center max-md:justify-center max-sm:gap-[15px]"
                      >
                        {Object.values(socialMediaIcons)?.map((icon) => (
                          <Avatar
                            key={icon.id}
                            className="w-7 h-7"
                            src={icon}
                          />
                        ))}
                      </Space>
                    </Space>
                  </div>
                </>
              )}
            </>
            {/* ) : ( */}
          </Content>
        </Layout>
      </Layout>
      <RightDrawer
        user={user_details}
        visible={visible}
        setVisible={setVisible}
        setRefresh={props?.setRefresh}
      />
      <UserAddress open={open} setOpen={setOpen} />
    </>
  );
};

export default BillingLayout;
