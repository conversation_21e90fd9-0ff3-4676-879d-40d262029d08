/* eslint-disable react/jsx-closing-bracket-location */
/* eslint-disable react/jsx-one-expression-per-line */
/* eslint-disable comma-dangle */
import {
  Link,
  Outlet,
  useLocation,
  useNavigate,
  useParams,
} from "react-router-dom";
import {
  Avatar,
  Button,
  Col,
  Image,
  Layout,
  Menu,
  Row,
  Select,
  Spin,
  Tooltip,
} from "antd";
import React, { useEffect, useState } from "react";
import { LoadingOutlined } from "@ant-design/icons";
import Sider from "antd/es/layout/Sider";
import CONSTANTS, {
  appRoot,
  loginRoot,
  teamRoot,
} from "../../util/constant/CONSTANTS";
import { getAuthToken } from "../../util/API/authStorage";
import Bplain from "../../asset/image/Bplain.svg";
import ASP from "../../asset/image/ASP.svg";
import logo from "../../asset/logos/icon.svg";
import RightDrawer from "./Component/right-drawer";
import { menu, mobileMenu } from "../../util/constant/menu";
import AutoBreadcrumb from "../Breadcrumb";
import {
  Bell,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  LogOut,
  Plane,
  Rocket,
  UserRoundPlus,
} from "lucide-react";
import GradientButton from "../common/GradientButton";
import useHttp from "../../hooks/use-http";
import { apiGenerator } from "../../util/functions";

const titles = {
  "/team/:teamId": "Write Post",
  "/team/:teamId/media": "Media Management",
  "/team/:teamId/posts": "Your Generated Posts",
  "/team/:teamId/posts/:postId": "Your Generated Posts",
  "/team/:teamId/billing": "Billing & Subscriptions",
  "/team/:teamId/settings/:media": "Platform Settings",
};

const { Header, Content } = Layout;
const TeamLayout = (props) => {
  const { user_details } = props;
  const { teamId, postId, media } = useParams();
  const API = useHttp();
  const subscriptionPlan = props?.user_details?.plan;
  const navigate = useNavigate();
  const location = useLocation();
  const currentPath = location.pathname;
  const [visible, setVisible] = useState(false);
  const [teams, setTeams] = useState([]);
  const breadcrumbItems = [];
  const [collapsed, setCollapsed] = useState(false);
  const pageTitle =
    Object.entries(titles)?.find(([key]) =>
      new RegExp(
        `^${key
          .replace(":teamId", "\\d+") // Match numbers for teamId
          .replace(":media", "[\\w]+") // Match numbers for teamId
          .replace(":postId", "[\\w-]+")}$` // Match alphanumeric + hyphen for postId
      ).test(currentPath)
    )?.[1] || "Default Title";

  useEffect(() => {
    if (!(getAuthToken() !== undefined && getAuthToken() !== null)) {
      navigate(loginRoot);
      return;
    }
    API.sendRequest(CONSTANTS.API.teams.getAll, (res) => {
      setTeams(
        res?.data?.response?.map((projectEl) => ({
          label: projectEl?.name,
          value: projectEl?.id,
          key: projectEl?.id,
        }))
      );
    });
  }, []);

  const connectAllMedia = (e) => {
    e.stopPropagation();
    if (!teamId) return;
    API.sendRequest(
      apiGenerator(
        CONSTANTS.API.connectMedia.connectAll,
        {},
        `?teamId=${teamId}&redirectUrl=${window.location.href}&logoUrl=${process.env.REACT_APP_PORTAL_LOGO_LINK}`
      ),
      (res) => {
        if (res?.url) window.open(res?.url, "_blank");
      }
    );
  };

  const onProjectChange = (newTeamId) => {
    const currentPath = location.pathname;

    let updatedPath;

    if (/^\/team\/\d+\/posts\/[^/]+$/.test(currentPath)) {
      // If path is /team/:teamId/posts/:postId
      updatedPath = currentPath.replace(
        /\/team\/\d+\/posts\/[^/]+$/,
        `/team/${newTeamId}/posts`
      );
    } else {
      // General case: replace just the team ID
      updatedPath = currentPath.replace(/\/team\/\d+/, `/team/${newTeamId}`);
    }

    if (updatedPath !== currentPath) {
      navigate(updatedPath);
    }
  };

  const menuProps = {
    items: teams,
    onClick: onProjectChange,
  };

  const onMenuChange = (e) => {
    if (e.keyPath.length) {
      navigate(
        mobileMenu[+e.key.replace("tmp-", "")]?.path
          ?.replace(":teamId", teamId)
          .replace(":media", media || "company")
      );
    }
  };

  const currantProject = teams?.find((projectEl) => projectEl?.key === +teamId);

  let activeMenuItemIndex = mobileMenu.findIndex(
    (item) =>
      currentPath ===
      item.path.replace(":teamId", teamId).replace(":media", media || "company")
  );

  if (currentPath === `${teamRoot}/${teamId}/media`) {
    breadcrumbItems.push({
      title: (
        <Link
          to={`${teamRoot}/${teamId}`}
          className="max-w-[90px] md:max-w-fit truncate"
        >
          {currantProject?.label}
        </Link>
      ),
    });
    breadcrumbItems.push({
      title: "Media Management",
    });
  } else if (currentPath === `${teamRoot}/${teamId}/posts`) {
    breadcrumbItems.push({
      title: (
        <Link
          to={`${teamRoot}/${teamId}`}
          className="max-w-[90px] md:max-w-fit truncate"
        >
          {currantProject?.label}
        </Link>
      ),
    });
    breadcrumbItems.push({
      title: "Posts",
    });
  } else if (currentPath === `${teamRoot}/${teamId}/posts/${postId}`) {
    breadcrumbItems.push({
      title: (
        <Link
          to={`${teamRoot}/${teamId}`}
          className="max-w-[90px] md:max-w-fit truncate"
        >
          {currantProject?.label}
        </Link>
      ),
    });
    breadcrumbItems.push({
      title: <Link to={`${teamRoot}/${teamId}/posts`}>Posts</Link>,
    });
    breadcrumbItems.push({
      title: "Detail Page",
    });
  } else if (currentPath === `${teamRoot}/${teamId}/billing`) {
    breadcrumbItems.push({
      title: (
        <Link
          to={`${teamRoot}/${teamId}`}
          className="max-w-[90px] md:max-w-fit truncate"
        >
          {currantProject?.label}
        </Link>
      ),
    });
    breadcrumbItems.push({
      title: "Billing",
    });
  } else if (currentPath === `${teamRoot}/${teamId}/settings/${media}`) {
    breadcrumbItems.push({
      title: (
        <Link
          to={`${teamRoot}/${teamId}`}
          className="max-w-[90px] md:max-w-fit truncate"
        >
          {currantProject?.label}
        </Link>
      ),
    });
    breadcrumbItems.push({
      title: "Settings",
    });
  } else {
    breadcrumbItems.push({
      title: (
        <div className="max-w-[150px] md:max-w-fit truncate">
          {currantProject?.label}
        </div>
      ),
    });
    breadcrumbItems.push({
      title: "Write Post",
    });
  }

  // Place it Below breadcrumbItems
  activeMenuItemIndex = `tmp-${
    activeMenuItemIndex > -1 ? activeMenuItemIndex : 2
  }`;

  const handleLogout = () => {
    localStorage.clear();
    navigate("/");
  };

  return (
    <>
      <Layout>
        <Layout>
          <Layout
            hasSider
            style={{
              minHeight: "100vh",
            }}
          >
            <Sider
              className={`hidden lg:block !sticky top-0 h-[100vh]`}
              collapsible
              trigger={null}
              collapsed={collapsed}
              // onCollapse={(value) => setCollapsed(value)}
              theme="light"
              style={{
                border: "1px solid #d9dbdf",
              }}
            >
              {!collapsed ? (
                <div className="flex justify-center">
                  <Image
                    style={{
                      height: "70px",
                      width: 150,
                      cursor: "pointer",
                    }}
                    preview={false}
                    src={logo}
                    onClick={() => {
                      navigate(`${appRoot}`);
                    }}
                  />
                </div>
              ) : (
                <div className="flex justify-center">
                  <Image
                    style={{ height: 70, cursor: "pointer", width: 50 }}
                    preview={false}
                    src={ASP}
                    onClick={() => {
                      navigate(`${appRoot}`);
                    }}
                  />
                </div>
              )}
              <Menu
                selectedKeys={activeMenuItemIndex}
                activeKey=""
                inlineCollapsed={collapsed}
                items={menu}
                onClick={onMenuChange}
                expandIcon
                className="hidden lg:block"
                mode="inline"
              />
              {collapsed ? (
                <div className="w-full p-2 absolute bottom-0">
                  <Button
                    type="default"
                    size="large"
                    className="w-full mb-2 border-0"
                    onClick={handleLogout}
                  >
                    <LogOut strokeWidth={1.5} color="#fb3748" size={20} />
                  </Button>
                </div>
              ) : (
                <div className="w-full p-2 absolute bottom-0 flex gap-2">
                  <Button
                    type="default"
                    size="large"
                    className="text-[#a855f7] text-sm border-0 !hover:bg-transparent !hover:text-[#a855f7] shadow-none font-medium"
                    onClick={handleLogout}
                    icon={
                      <LogOut strokeWidth={1.5} color="#a855f7" size={20} />
                    }
                  >
                    Logout
                  </Button>
                </div>
              )}

              <GradientButton
                className="absolute top-[82px] right-0 translate-x-[50%] flex items-center justify-center"
                shape="circle"
                size="small"
                child={
                  <>
                    <div className="flex">
                      {!collapsed ? (
                        <ChevronLeft strokeWidth={1.5} size={16} color="#fff" />
                      ) : (
                        <ChevronRight
                          strokeWidth={1.5}
                          size={16}
                          color="#fff"
                        />
                      )}
                    </div>
                  </>
                }
                onClick={() => setCollapsed(!collapsed)}
              />
            </Sider>
            <Layout className="site-layout">
              <Header
                className="top-0 sticky z-10  px-0 lg:p-0 pb-4 flex flex-col justify-center h-auto md:h-[110px]  lg:h-[70px] header-res"
                style={{
                  backgroundColor: "#FFFFFF",
                  border: "1px solid #d9dbdf",
                  borderLeft: "none",
                }}
              >
                {/* Web View */}
                <Row className="md:px-[50px] px-[10px] w-full">
                  <Col
                    span={24}
                    className="flex justify-center items-center h-14 pb-1 lg:hidden"
                  >
                    <Image
                      style={{
                        height: 40,
                        width: 170,
                        cursor: "pointer",
                      }}
                      preview={false}
                      src={logo}
                      onClick={() => {
                        navigate(`${appRoot}`);
                      }}
                    />
                  </Col>
                  <Col span={24} className="sm:flex justify-between hidden ">
                    <div className="flex items-center gap-4">
                      <Select
                        optionFilterProp="label"
                        className="h-10 min-w-40 mobile:w-[150px]"
                        placement={"bottomLeft"}
                        value={teams?.length ? +teamId : null}
                        popupMatchSelectWidth={false}
                        options={teams}
                        onSelect={onProjectChange}
                        suffixIcon={
                          <ChevronDown
                            size={22}
                            strokeWidth={1.5}
                            color="#828997"
                          />
                        }
                      />
                      <Button
                        variant="outlined"
                        color="pink"
                        className="h-10"
                        icon={
                          <UserRoundPlus
                            strokeWidth={1.5}
                            color="#ec4899"
                            size={20}
                          />
                        }
                        onClick={connectAllMedia}
                      >
                        <span className="text-[#ec4899] md:block hidden">
                          Connect Social Accounts
                        </span>
                      </Button>
                    </div>
                    <div className="flex items-center gap-4">
                      <Button
                        shape={"round"}
                        variant="outlined"
                        className="h-9 !px-3"
                        color="primary"
                        onClick={() => navigate("/billing")}
                      >
                        <div className="flex items-center gap-2 justify-between">
                          <div className="w-5 flex items-center">
                            {subscriptionPlan?.name === "Lite" ? (
                              <Image src={Bplain} width={20} preview={false} />
                            ) : subscriptionPlan?.name === "Pro" ? (
                              <Plane
                                color="#a855f7"
                                size={24}
                                strokeWidth={1.5}
                              />
                            ) : (
                              <Rocket
                                strokeWidth={1.5}
                                color="#a855f7"
                                size={24}
                              />
                            )}
                          </div>
                          <span className="text-[#a855f7] text-sm font-medium">
                            {subscriptionPlan?.name}
                          </span>
                        </div>
                      </Button>
                      {/* <Tooltip className="cursor-pointer" title="Usage">
                        <Bell
                          strokeWidth={1.5}
                          size={24}
                          className="cursor-pointer"
                          color="#6b7280"
                        />
                      </Tooltip> */}
                      <Avatar
                        size={36}
                        className="bg-[#374151] shadow-lg cursor-pointer"
                        shape="circle"
                        onClick={() => {
                          setVisible(true);
                        }}
                      >
                        <div className="font-semibold text-sm">
                          {user_details?.name?.charAt(0).toUpperCase()}
                        </div>
                      </Avatar>
                    </div>
                  </Col>
                  <Col span={24} className="flex sm:hidden items-center gap-5">
                    <Select
                      optionFilterProp="label"
                      className="h-10 w-full"
                      placement={"bottomLeft"}
                      value={teams?.length ? +teamId : null}
                      popupMatchSelectWidth={false}
                      options={teams}
                      onSelect={onProjectChange}
                      suffixIcon={
                        <ChevronDown
                          size={22}
                          strokeWidth={1.5}
                          color="#828997"
                        />
                      }
                      dropdownStyle={{
                        maxWidth: "calc(100vw - 30px)", // fixed spacing typo
                        maxHeight: 300, // add max height to avoid y-overflow on mobile
                        overflowX: "auto",
                        overflowY: "auto", // allow internal scrolling instead of body scroll
                      }}
                    />
                    <div className="flex gap-5 items-center">
                      <Button
                        variant="outlined"
                        color="pink"
                        className="h-10"
                        icon={
                          <UserRoundPlus
                            strokeWidth={1.5}
                            color="#ec4899"
                            size={20}
                          />
                        }
                        onClick={connectAllMedia}
                      >
                        <span className="text-[#ec4899] md:block hidden">
                          Connect Social Accounts
                        </span>
                      </Button>
                      <Button
                        shape={"round"}
                        variant="outlined"
                        className="h-9 !px-3"
                        color="primary"
                        onClick={() => navigate("/billing")}
                      >
                        <div className="flex items-center gap-2 justify-between">
                          <div className="w-5 flex items-center">
                            {subscriptionPlan?.name === "Lite" ? (
                              <Image src={Bplain} width={20} preview={false} />
                            ) : subscriptionPlan?.name === "Pro" ? (
                              <Plane
                                color="#a855f7"
                                size={24}
                                strokeWidth={1.5}
                              />
                            ) : (
                              <Rocket
                                strokeWidth={1.5}
                                color="#a855f7"
                                size={24}
                              />
                            )}
                          </div>
                          <span className="text-[#a855f7] text-sm font-medium">
                            {subscriptionPlan?.name}
                          </span>
                        </div>
                      </Button>
                      {/* <Tooltip className="cursor-pointer" title="Usage">
                        <Bell
                          strokeWidth={1.5}
                          size={24}
                          className="cursor-pointer"
                          color="#6b7280"
                        />
                      </Tooltip> */}
                      <div className="h-9 flex items-center w-9">
                        <Avatar
                          size={36}
                          className="bg-[#374151] shadow-lg cursor-pointer"
                          shape="circle"
                          onClick={() => {
                            setVisible(true);
                          }}
                        >
                          <div className="font-semibold text-sm">
                            {user_details?.name?.charAt(0).toUpperCase()}
                          </div>
                        </Avatar>
                      </div>
                    </div>
                  </Col>
                </Row>
              </Header>
              <Content className="md:m-[50px] m-6 mb-[100px] mobilescale">
                <Row>
                  <div className="text-[#a855f7] text-3xl font-semibold w-full">
                    {pageTitle}
                  </div>
                  <div className="mt-5 md:mb-[50px] mb-5">
                    <AutoBreadcrumb items={breadcrumbItems} />
                  </div>
                </Row>

                <Outlet />
              </Content>

              <Menu
                className="flex justify-center fixed bottom-0 shadow-lg w-full z-50 lg:hidden bg-red border border-gray-300 rounded-tl-[20px] rounded-tr-[20px] h-[60px]"
                selectedKeys={activeMenuItemIndex}
                mode="horizontal"
                items={mobileMenu}
                onClick={onMenuChange}
              />
            </Layout>
          </Layout>
        </Layout>
      </Layout>
      <RightDrawer
        user={user_details}
        visible={visible}
        setVisible={setVisible}
        setRefresh={props?.setRefresh}
      />
    </>
  );
};

export default TeamLayout;
