import {
  Avatar,
  Button,
  Card,
  Checkbox,
  Col,
  Empty,
  Form,
  Image,
  Input,
  Modal,
  notification,
  Popconfirm,
  Popover,
  Progress,
  Radio,
  Row,
  Select,
  Space,
  Spin,
  Tag,
  Upload,
} from "antd";
import {
  Eye,
  Info,
  Phone,
  Play,
  Plus,
  Image as ImageIcon,
  Search,
  Trash2,
} from "lucide-react";
import React, { useCallback, useEffect, useRef, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import GradientButton from "../common/GradientButton";
import Team from "./Team";
import { IoCloseOutline } from "react-icons/io5";
import { IoMdInformationCircleOutline } from "react-icons/io";
import web from "../../asset/image/web.svg";
import com from "../../asset/image/com.svg";
import team from "../../asset/image/team.svg";
import teamC from "../../asset/image/teamC.svg";
import rightIcon from "../../asset/image/Group 22.svg";
import useHttp from "../../hooks/use-http";
import CONSTANTS from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import { LoadingOutlined, SearchOutlined } from "@ant-design/icons";
import MediaPreview from "../common/MediaPreview";
import { useNavigate } from "react-router-dom";

const twoColors = {
  "0%": "#108ee9",
  "100%": "#87d068",
};

const TeamManagement = ({ isSpecialUser, isNeedMediaWizard }) => {
  const API = useHttp();
  const APITeam = useHttp();
  const navigate = useNavigate();
  const API2 = useHttp(); // For infinite scroll additional requests
  const [form] = Form.useForm();
  const [open, setOpen] = useState(null);
  const [openSuccessModal, setOpenSuccessModal] = useState(false);
  const [teams, setTeams] = useState([]);
  const [teamsListWizard, setTeamsListWizard] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const debounceRef = useRef(null);
  const [mediaWizard, setMediaWizard] = useState(null);
  const [selectedAction, setSelectedAction] = useState("add");
  const [selectedTeams, setSelectedTeams] = useState(new Set());
  const [searchQuery, setSearchQuery] = useState("");
  const [fileList, setFileList] = useState([]);
  const [progress, setProgress] = useState(0);
  const [failedImage, setFailedImage] = useState({});
  const [openPreview, setOpenPreview] = useState(null);

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedTeams(new Set(teams.map((team) => team.id)));
    } else {
      setSelectedTeams(new Set());
    }
  };

  const getAllTeamForWizard = async () => {
    const limit = 1000;
    let page = 1;
    let allTeams = [];

    while (true) {
      const queryParams = `?limit=${limit}&page=${page}`;

      const res = await new Promise((resolve, reject) => {
        API.sendRequest(
          apiGenerator(CONSTANTS.API.teams.getAll, {}, queryParams),
          resolve,
          reject
        );
      });

      const data = res?.data;

      // Add to allTeams
      allTeams.push(...(data?.response || []));

      // Break if all data fetched
      if (page * limit >= data?.count) break;
      page++;
    }

    setTeamsListWizard(allTeams);
  };

  useEffect(() => {
    if (mediaWizard === "main") getAllTeamForWizard();
  }, [mediaWizard]);

  const closeMediaWizard = (modal) => {
    setSearchQuery("");
    setSelectedAction("add");
    setSelectedTeams(new Set());
    setFileList([]);
    setFailedImage({});
    setMediaWizard(modal);
  };

  const handleTeamSelect = (teamId, checked) => {
    const newSelected = new Set(selectedTeams);
    if (checked) {
      newSelected.add(teamId);
    } else {
      newSelected.delete(teamId);
    }
    setSelectedTeams(newSelected);
  };
  const filteredTeams = teamsListWizard?.filter((team) =>
    team.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    if (!openSuccessModal) return;

    const timeoutId = setTimeout(() => {
      setOpenSuccessModal(false);
    }, 5000);

    return () => clearTimeout(timeoutId);
  }, [openSuccessModal]);

  const onCreateTeam = (value) => {
    const payload = {
      name: value?.name?.trim(),
      businessInfo: value?.businessInfo?.trim() || null,
      website: value?.website?.trim() || null,
      state: value?.state || null,
      city: value?.city?.trim() || null,
      phoneNumber: value?.phoneNumber?.trim() || null,
    };

    const isEdit = !!Object.keys(open || {})?.length;
    const endPoint = isEdit
      ? apiGenerator(CONSTANTS.API.teams.update, { teamId: open?.id })
      : CONSTANTS.API.teams.add;

    API.sendRequest(
      endPoint,
      (res) => {
        // Reset pagination and refresh data from API
        setPage(1);
        setHasMore(true);

        if (isEdit) {
          // For edit, refresh the current page to show updated data
          getAllTeam(1, searchTerm);
        } else {
          // For create, reset search and go to first page to show new team
          setSearchTerm("");
          getAllTeam(1, "");
          setOpenSuccessModal(true);
        }

        form.resetFields();
        setOpen(null);
      },
      payload,
      isEdit ? "Team updated successfully." : ""
    );
  };

  // Debounced search effect
  useEffect(() => {
    if (debounceRef.current) clearTimeout(debounceRef.current);

    debounceRef.current = setTimeout(() => {
      // Reset pagination when search changes
      setTeams([]);
      setPage(1);
      setHasMore(true);
      getAllTeam(1, searchTerm);
    }, 300);

    return () => clearTimeout(debounceRef.current);
  }, [searchTerm]);

  const handleChange = (field) => {
    const nameError = form.getFieldError(field);
    if (nameError.length) {
      form.validateFields([field]);
    }
  };

  const getAllTeam = useCallback((pageNum = 1, search = "") => {
    const limit = 20;
    const apiInstance = pageNum === 1 ? APITeam : API2;

    const queryParams = `?limit=${limit}&page=${pageNum}${
      search ? `&search=${encodeURIComponent(search)}` : ""
    }`;

    apiInstance.sendRequest(
      apiGenerator(CONSTANTS.API.teams.getAll, {}, queryParams),
      (res) => {
        const newTeams = res?.data?.response || [];

        if (pageNum === 1) {
          setTeams(newTeams);
        } else {
          setTeams((prevTeams) => [...prevTeams, ...newTeams]);
        }

        setHasMore(newTeams.length === limit);
      }
    );
  }, []);

  const loadMore = () => {
    if (!hasMore || APITeam.isLoading || API2.isLoading) return;
    const nextPage = page + 1;
    setPage(nextPage);
    getAllTeam(nextPage, searchTerm);
  };

  const onDelete = (id) => {
    API.sendRequest(
      apiGenerator(CONSTANTS.API.teams.delete, { teamId: id }),
      (res) => {
        // Refresh data from API to maintain consistency
        setPage(1);
        setHasMore(true);
        getAllTeam(1, searchTerm);
      },
      {},
      "Team deleted successfully."
    );
  };

  const handleMediaWizard = () => {
    if (selectedAction === "delete") {
      API.sendRequest(
        CONSTANTS.API.media.deleteMultipleUnusedMedia,
        (res) => {
          closeMediaWizard("delete");
        },
        { teamIds: Array.from(selectedTeams)?.join(",") },
        "Unused media deleted successfully."
      );
    } else if (selectedAction === "add") {
      if (!fileList?.length) {
        notification.info({
          message: "Please upload at least one file.",
          duration: 3,
        });
        return;
      }
      const formData = new FormData();
      fileList.forEach((item) => {
        formData.append("file", item.file);
      });
      formData.append("teamIds", Array.from(selectedTeams).join(","));

      API.sendRequest(
        CONSTANTS.API.media.uploadMultiple,
        (res) => {
          if (Object.keys(res?.results)?.length) {
            setProgress(0);
            setMediaWizard("uploadError");
            setFailedImage(res?.results);
          } else {
            setProgress(0);
            closeMediaWizard("upload");
          }
        },
        formData,
        null,
        () => {},
        (progressData) => setProgress(progressData?.percentage)
      );
    }
  };

  useEffect(() => {
    form.setFieldsValue(open);
  }, [open]);

  const handleUploadChange = async ({ file }) => {
    const id = `${file.uid}-${Date.now()}`;
    const preview = URL.createObjectURL(file);
    const newFile = {
      id,
      file,
      preview,
      progress: 0,
      uploading: true,
    };

    setFileList((prev) => [...prev, newFile]);

    // Simulate upload progress
    const simulateProgress = () => {
      let percent = 0;
      const interval = setInterval(() => {
        percent += 10;
        setFileList((prev) =>
          prev.map((f) => (f.id === id ? { ...f, progress: percent } : f))
        );
        if (percent >= 100) {
          clearInterval(interval);
          setFileList((prev) =>
            prev.map((f) => (f.id === id ? { ...f, uploading: false } : f))
          );
        }
      }, 100);
    };
    simulateProgress();
  };

  const handleDelete = (id) => {
    setFileList((prev) => prev.filter((f) => f.id !== id));
  };

  const beforeUpload = (file) => {
    const imageTypes = ["image/jpeg", "image/jpg", "image/png"];
    const videoTypes = ["video/mp4"];

    const isImage = imageTypes.includes(file.type);
    const isVideo = videoTypes.includes(file.type);

    if (!isImage && !isVideo) {
      notification.info({
        message: "Only JPG, JPEG, PNG images and MP4 videos are allowed.",
        duration: 3,
      });
      return false;
    }

    if (isImage && file.size / 1024 / 1024 > 25) {
      notification.info({
        message: "Image file size must be less than 25MB.",
        duration: 3,
      });
      return false;
    }

    if (isVideo && file.size / 1024 / 1024 > 1024) {
      notification.info({
        message: "Video file size must be less than 1GB.",
        duration: 3,
      });
      return false;
    }

    return true;
  };

  return (
    <>
      <Card
        className="rounded-[30px] mt-[30px]"
        styles={{ header: { padding: 30 }, body: { padding: 30 } }}
        title={
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
            <div className="flex gap-2 items-center">
              <div className="text-2xl sm:text-3xl font-semibold">
                Team Management
              </div>
              <Popover
                content="Easily manage teams, set rules, and connect social accounts in one place."
                trigger="hover"
              >
                <Info
                  strokeWidth={1.5}
                  size={24}
                  color="#6B7280"
                  className="cursor-pointer"
                />
              </Popover>
            </div>
            <div className="flex sm:flex-nowrap flex-wrap gap-3 mt-4 xs:mt-0">
              <Input
                size="large"
                placeholder="Search team"
                className="h-[48px] w-full sm:w-auto"
                prefix={<Search strokeWidth={1.5} color="#6b7280" />}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {isNeedMediaWizard && (
                <Button
                  size="large"
                  className="h-[48px] px-8 w-full sm:w-auto font-medium"
                  onClick={() => {
                    setMediaWizard("main");
                  }}
                >
                  Media Wizard
                </Button>
              )}
              {isNeedMediaWizard && (
                <Button
                  size="large"
                  className="h-[48px] px-8 w-full sm:w-auto font-medium"
                  onClick={() => navigate("/bulkpost")}
                >
                  Bulk Post
                </Button>
              )}
              <GradientButton
                icon={<Plus strokeWidth={1.5} color="#ffffff" size={22} />}
                buttonText="Create New Team"
                className="w-full sm:w-auto"
                onClick={() => setOpen({})}
              />
            </div>
          </div>
        }
      >
        <Spin
          spinning={APITeam?.isLoading && page === 1}
          indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
        >
          {!APITeam?.isLoading || teams?.length > 0 ? (
            teams?.length > 0 ? (
              <InfiniteScroll
                dataLength={teams?.length}
                next={loadMore}
                hasMore={hasMore}
                style={{ overflow: "visible" }}
                loader={
                  <div className="text-center py-4">
                    <Spin
                      indicator={
                        <LoadingOutlined style={{ fontSize: 24 }} spin />
                      }
                    />
                    <p className="text-base mt-2">Loading more teams...</p>
                  </div>
                }
                endMessage={
                  <div className="text-center py-4">
                    <p className="text-base text-gray-500">
                      {searchTerm
                        ? "No more teams found for your search."
                        : "You have seen all teams."}
                    </p>
                  </div>
                }
              >
                <div style={{ width: "100%", overflowX: "hidden" }}>
                  <Row gutter={[24, 24]} className="min-h-80">
                    {teams?.map((tm) => (
                      <Col
                        span={8}
                        xs={24}
                        sm={24}
                        md={24}
                        lg={12}
                        xl={8}
                        xxl={6}
                        key={tm?.id}
                      >
                        <Team
                          teamD={tm}
                          onEdit={() => setOpen(tm)}
                          onDelete={() => onDelete(tm?.id)}
                          API={APITeam}
                          isSpecialUser={isSpecialUser}
                        />
                      </Col>
                    ))}
                  </Row>
                </div>
              </InfiniteScroll>
            ) : (
              <div className="w-full min-h-80 flex justify-center items-center">
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={
                    searchTerm
                      ? "No teams found for your search."
                      : "No teams found."
                  }
                />
              </div>
            )
          ) : null}
        </Spin>
      </Card>
      <Modal
        open={open}
        closeIcon={false}
        footer={null}
        centered
        onCancel={() => setOpen(null)}
        maskClosable={false}
        width={650}
        style={{ zoom: 0.9 }}
        className="rounded-[20px] md:rounded-[40px] overflow-hidden p-0 auth-modal"
        styles={{
          content: { padding: 0 },
          body: { padding: "50px" },
          mask: { backgroundColor: "rgba(0, 0, 0, 0.8)" },
        }}
      >
        <>
          <div className="flex items-center justify-between">
            <div className="sm:text-3xl text-2xl font-semibold text-[#020817]">
              {Object?.keys(open || {})?.length
                ? "Edit Team"
                : "Create New Team"}
            </div>
            <IoCloseOutline
              size={32}
              color="#9ca8bc"
              className="cursor-pointer mt-[6px]"
              onClick={() => {
                form.resetFields();
                setOpen(null);
              }}
            />
          </div>
          <div className="text-sm font-normal text-[#6b7280] mb-10 pe-10">
            {Object?.keys(open || {})?.length
              ? "Edit and manage your team information."
              : "Add details to create a new team to maintain your social media posts."}
          </div>
          <Form
            name="team_m"
            onFinish={onCreateTeam}
            form={form}
            validateTrigger="onBlur"
          >
            <p className="font-medium text-base text-[#020817] required-field mb-2">
              Team Name<span className="text-red-600 ml-1">*</span>
            </p>
            <Form.Item
              className="mb-4"
              name="name"
              rules={[
                { required: true, message: "Please Enter Your Team Name!" },
                {
                  min: 3,
                  message: "Name must be at least 3 characters long!",
                },
                {
                  max: 80,
                  message: "Name cannot exceed 80 characters!",
                },
                {
                  whitespace: true,
                  message: "Input cannot be empty or spaces only",
                },
              ]}
            >
              <Input
                prefix={
                  <Image
                    src={team}
                    alt="Team Icon"
                    preview={false}
                    width={22}
                  />
                }
                placeholder="enter team name"
                autoComplete="off"
                onChange={() => handleChange("name")}
              />
            </Form.Item>

            <p className="font-medium text-base text-[#020817] required-field mb-2">
              Website Name
            </p>
            <Form.Item
              className="mb-4"
              name="website"
              rules={[
                { required: false, message: "Please Enter Your Team Name!" },
                {
                  whitespace: true,
                  message: "Input cannot be empty or spaces only",
                },
                {
                  max: 80,
                  message: "Website cannot exceed 80 characters!",
                },
              ]}
            >
              <Input
                prefix={
                  <Image
                    src={web}
                    alt="Website Icon"
                    preview={false}
                    width={22}
                  />
                }
                placeholder="enter website name"
                onChange={() => handleChange("website")}
                autoComplete="off"
              />
            </Form.Item>
            {isSpecialUser && (
              <>
                <p className="font-medium text-base text-[#020817] required-field mb-2">
                  Mobile Number
                </p>
                <Form.Item
                  className="mb-0"
                  name="phoneNumber"
                  rules={[
                    {
                      required: false,
                      message: "Please enter your mobile number!",
                    },
                  ]}
                >
                  <Input
                    prefix={<Phone strokeWidth={1.5} color="#6b7280" />}
                    placeholder="enter mobile number"
                    autoComplete="off"
                    onChange={(e) => {
                      const onlyDigits = e.target.value.replace(/\D/g, "");
                      form.setFieldsValue({ mobile: onlyDigits }); // optional
                    }}
                  />
                </Form.Item>

                <div className="flex flex-col md:flex-row gap-4">
                  {/* State Dropdown */}
                  <div className="w-full md:w-1/2">
                    <p className="font-medium text-base text-[#020817] required-field mb-2">
                      State
                    </p>
                    <Form.Item
                      name="state"
                      className="mb-0"
                      rules={[{ required: false }]}
                    >
                      <Select
                        showSearch
                        allowClear
                        placeholder="Select state"
                        onChange={() => handleChange("state")}
                        optionFilterProp="label"
                        filterOption={(input, option) =>
                          (option?.label ?? "")
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        options={CONSTANTS.states}
                      />
                    </Form.Item>
                  </div>

                  {/* City Input */}
                  <div className="w-full md:w-1/2">
                    <p className="font-medium text-base text-[#020817] required-field mb-2">
                      City
                    </p>
                    <Form.Item
                      name="city"
                      className="mb-0"
                      rules={[{ required: false }]}
                    >
                      <Input
                        placeholder="enter city name"
                        onChange={() => handleChange("city")}
                        autoComplete="off"
                      />
                    </Form.Item>
                  </div>
                </div>
              </>
            )}

            <p className="font-medium text-base text-[#020817] required-field mb-2">
              Company Information
            </p>
            <div className="relative">
              <Form.Item className="mb-2" name="businessInfo">
                <Input.TextArea
                  className="pl-12 w-full"
                  placeholder="enter company information"
                  autoSize={{ minRows: 4, maxRows: 6 }}
                  onChange={() => handleChange("businessInfo")}
                />
              </Form.Item>
              <Avatar
                src={com}
                alt="Company Icon"
                preview={false}
                shape="square"
                size={25}
                className="absolute left-[10px] top-3 z-10"
              />
            </div>
            <div className="flex items-center gap-1 text-sm font-normal text-[#6b7280]">
              <IoMdInformationCircleOutline size={20} />
              <span>
                These details will be used as context during post generation.
              </span>
            </div>
            <Form.Item className="mt-[50px] mb-0">
              <div className="flex gap-4">
                <GradientButton
                  htmlType="submit"
                  className="w-1/2 "
                  buttonText={
                    Object.keys(open || {})?.length ? "Save" : "Create"
                  }
                  loading={API?.isLoading}
                />
                <Button
                  className="w-1/2"
                  onClick={() => {
                    form.resetFields();
                    setOpen(null);
                  }}
                >
                  Cancel
                </Button>
              </div>
            </Form.Item>
          </Form>
        </>
      </Modal>
      <Modal
        open={openSuccessModal}
        closeIcon={false}
        footer={null}
        centered
        onCancel={() => setOpenSuccessModal(false)}
        maskClosable={false}
        style={{ zoom: 0.9 }}
        width={500}
        className="rounded-[40px] overflow-hidden p-0 auth-modal"
        styles={{
          content: { padding: 0 },
          body: { padding: "50px" },
          mask: { backgroundColor: "rgba(0, 0, 0, 0.8)" },
        }}
      >
        <div className="mobilescale">
          <div className="flex flex-col mb-[50px]">
            <div className="flex justify-center">
              <Image src={teamC} width={90} preview={false} />
            </div>
            <div className="text-4xl font-semibold text-center mt-[30px]">
              Team Successfully Created
            </div>
            <div className="text-lg text-[#6B7280] text-center mt-5">
              Congratulations! 🎉
            </div>
            <div className="text-lg text-[#6B7280] text-center mt-2">
              Your team has been successfully created! Connect your social media
              accounts to start posting.
            </div>
          </div>
          <GradientButton
            className="w-full"
            buttonText="Get Started"
            onClick={() => setOpenSuccessModal(false)}
          />
        </div>
      </Modal>

      <Modal
        open={mediaWizard}
        closeIcon={false}
        footer={null}
        centered
        onCancel={() => closeMediaWizard(null)}
        maskClosable={false}
        width={["delete", "upload"].includes(mediaWizard) ? 500 : 1000}
        style={{ zoom: 0.9 }}
        className="rounded-[20px] md:rounded-[40px] overflow-hidden p-0 auth-modal"
        styles={{
          content: { padding: 0 },
          body: { padding: "50px" },
          mask: { backgroundColor: "rgba(0, 0, 0, 0.8)" },
        }}
      >
        {mediaWizard === "main" && (
          <>
            <div className="flex items-center justify-between">
              <div className="sm:text-3xl text-xl font-semibold text-[#020817]">
                Manage Media Across Teams
              </div>
              <IoCloseOutline
                size={32}
                color="#9ca8bc"
                className="cursor-pointer mt-[6px]"
                onClick={() => {
                  closeMediaWizard(null);
                }}
              />
            </div>
            <div className="text-sm font-normal text-[#6b7280] md:mb-10 mb-6 pe-10">
              Easily upload or clean up media across multiple teams with just a
              few clicks.
            </div>
            <Row gutter={[24, 24]} className="flex-wrap">
              {/* TeamSelector */}
              <Col xs={24} md={12}>
                <div className="flex flex-col items-start gap-[15px] h-full">
                  <div className="text-black text-lg font-normal leading-[21.6px]">
                    Select Teams
                  </div>
                  <Input
                    prefix={<SearchOutlined className="text-gray-400" />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search teams..."
                    className="w-full h-12 rounded-[10px] border-[#D9DBDF]"
                    disabled={API?.isLoading}
                  />
                  <div className="flex items-center justify-between w-full">
                    <div className="select-all flex items-center gap-2">
                      <Checkbox
                        checked={selectedTeams.size === teams.length}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        className="custom-checkbox"
                        disabled={API?.isLoading}
                      />
                      <div className="text-base text-[#020817]">Select All</div>
                    </div>
                    {selectedTeams?.size > 0 && (
                      <div className="text-base text-[#a855f7] ms-2 flex items-center">
                        <Tag color="#a855f7" className="text-sm">
                          {selectedTeams.size}
                        </Tag>{" "}
                        Selected
                      </div>
                    )}
                  </div>
                  <div className="w-full md:h-[350px] md:max-h-[350px] max-h-24 overflow-y-auto pr-2 custom-scroll">
                    <Row gutter={[16, 10]}>
                      {filteredTeams.map((team) => (
                        <Col span={24} key={team.id}>
                          <div className="flex items-center gap-2">
                            <Checkbox
                              className="custom-checkbox"
                              checked={selectedTeams.has(team.id)}
                              onChange={(e) =>
                                handleTeamSelect(team.id, e.target.checked)
                              }
                              disabled={API?.isLoading}
                            />
                            <div className="truncate text-base text-[#020817] w-full">
                              {team.name}
                            </div>
                          </div>
                        </Col>
                      ))}
                    </Row>
                  </div>
                </div>
              </Col>

              {/* ActionSelector */}
              <Col xs={24} md={12}>
                <div className="flex flex-col items-start gap-[15px] h-full">
                  <div className="text-black text-lg font-normal leading-[21.6px]">
                    Select an Action
                  </div>
                  <div
                    className={`flex flex-col items-start gap-5 w-full ${
                      selectedAction !== "add" && "mb-5"
                    }`}
                  >
                    <Radio.Group
                      value={selectedAction}
                      onChange={(e) => setSelectedAction(e.target.value)}
                      className="w-full"
                    >
                      <Space
                        direction="vertical"
                        className="w-full md:gap-5 gap-3"
                      >
                        <div className="flex items-center w-full">
                          <Radio
                            value="add"
                            className="text-base font-normal text-[#020817]"
                            disabled={API?.isLoading}
                          >
                            Add media
                          </Radio>
                          <Popover
                            content="Upload media files to selected teams."
                            trigger="hover"
                          >
                            <Info
                              strokeWidth={1.5}
                              size={17}
                              color="#6B7280"
                              className="cursor-pointer"
                            />
                          </Popover>
                        </div>
                        <div className="flex items-center w-full">
                          <Radio
                            value="delete"
                            className="text-base font-normal text-[#020817]"
                            disabled={API?.isLoading}
                          >
                            Delete all unused media
                          </Radio>
                          <Popover
                            content="Remove unused media from selected teams."
                            trigger="hover"
                          >
                            <Info
                              strokeWidth={1.5}
                              size={17}
                              color="#6B7280"
                              className="cursor-pointer"
                            />
                          </Popover>
                        </div>
                      </Space>
                    </Radio.Group>
                  </div>

                  {selectedAction === "add" && (
                    <>
                      <div className="text-black text-lg font-normal leading-[21.6px] mt-3">
                        Add Media
                      </div>
                      <div className="max-h-[180px] md:max-h-[320px] overflow-y-auto pr-2 custom-scroll w-full">
                        <div className="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-3 gap-3 md:gap-4 min-h-24 w-full">
                          <Upload
                            listType="picture-card"
                            multiple
                            customRequest={({ file }) =>
                              handleUploadChange({ file })
                            }
                            showUploadList={false}
                            beforeUpload={beforeUpload}
                            disabled={API?.isLoading}
                          >
                            <div className="flex flex-col items-center justify-center">
                              <Plus
                                strokeWidth={1.5}
                                color="#6b7280"
                                size={20}
                              />
                              <div className="text-sm font-normal">Upload</div>
                            </div>
                          </Upload>

                          {fileList.map((item) => (
                            <Card
                              key={item.id}
                              className="relative p-0 overflow-hidden"
                              styles={{ body: { padding: 4 } }}
                            >
                              <div className="relative group w-full h-20 md:h-24">
                                {item.file?.type !== "video/mp4" ? (
                                  <img
                                    src={item.preview}
                                    alt="preview"
                                    className="object-cover w-full h-full rounded-md"
                                  />
                                ) : (
                                  <video
                                    src={item.preview}
                                    className="object-cover w-full h-full rounded-md"
                                    controls={false}
                                  />
                                )}

                                {/* Type icon */}
                                {item.file?.type !== "video/mp4" ? (
                                  <ImageIcon
                                    strokeWidth={1.5}
                                    size={18}
                                    color="#fff"
                                    className="top-1 right-1 absolute bg-black/50 rounded-[4px] p-[2px]"
                                  />
                                ) : (
                                  <Play
                                    strokeWidth={1.5}
                                    color="#fff"
                                    size={14}
                                    className="top-1 right-1 absolute bg-black/50 rounded-[4px] p-[4px]"
                                  />
                                )}

                                {/* Hover controls */}
                                {!API.isLoading && (
                                  <div className="absolute w-full h-full top-0 flex items-center justify-center gap-2 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-md">
                                    <Eye
                                      strokeWidth={1.5}
                                      size={20}
                                      color="#fff"
                                      className="cursor-pointer"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setOpenPreview({
                                          url: item?.preview,
                                          type:
                                            item?.file?.type !== "video/mp4"
                                              ? "image"
                                              : "video",
                                          name: item.file?.name,
                                          size: item.file?.size,
                                          width: item.file?.width,
                                          height: item.file?.height,
                                        });
                                      }}
                                    />
                                    <Popconfirm
                                      title="Delete file"
                                      description="Are you sure you want to delete this file?"
                                      icon={
                                        <Info
                                          strokeWidth={1.5}
                                          size={16}
                                          color="red"
                                          className="mt-[2px] me-1"
                                        />
                                      }
                                      okText="Yes"
                                      cancelText="No"
                                      onConfirm={(e) => {
                                        e.stopPropagation();
                                        handleDelete(item.id);
                                      }}
                                      onCancel={(e) => e.stopPropagation()}
                                    >
                                      <Trash2
                                        strokeWidth={1.5}
                                        size={20}
                                        color="#fff"
                                        className="cursor-pointer"
                                        onClick={(e) => e.stopPropagation()}
                                      />
                                    </Popconfirm>
                                  </div>
                                )}

                                {/* Upload progress bar */}
                                {item.uploading && (
                                  <div className="absolute top-1/2 w-[80%] left-[10%] h-[6px] bg-gray-200">
                                    <div
                                      className="bg-purple-500 h-full transition-all duration-300"
                                      style={{ width: `${item.progress}%` }}
                                    />
                                  </div>
                                )}
                              </div>
                            </Card>
                          ))}
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </Col>
            </Row>
            {!!progress && progress !== 100 && (
              <Progress
                percent={progress}
                strokeColor={twoColors}
                className="my-5"
              />
            )}
            {API?.isLoading && progress === 100 && (
              <div className="text-base font-normal text-red-500 py-5">
                Note: Uploading and processing large files (especially videos)
                may take up to 10–15 minutes depending on the file size.
              </div>
            )}
            {API?.isLoading && selectedAction !== "add" && (
              <div className="text-base font-normal text-red-500 py-5">
                Note: Deleting unused media may take some time depending on the
                number of files.
              </div>
            )}
            <div
              className={`w-full sm:w-1/2 grid grid-cols-2 sm:grid-cols-2 gap-3 ${
                API?.isLoading ? "" : "md:mt-10 mt-5"
              }`}
            >
              {selectedAction === "delete" ? (
                <Popconfirm
                  title="Delete file"
                  description="Are you sure you want to delete this file?"
                  icon={
                    <Info
                      strokeWidth={1.5}
                      size={16}
                      color="red"
                      className="mt-[2px] me-1"
                    />
                  }
                  okText="Yes"
                  cancelText="No"
                  onConfirm={(e) => {
                    e.stopPropagation();
                    handleMediaWizard();
                  }}
                  onCancel={(e) => e.stopPropagation()}
                >
                  <GradientButton
                    buttonText="Submit"
                    className="w-full"
                    loading={API?.isLoading}
                    disabled={!selectedTeams.size}
                    // onClick={() => handleMediaWizard()}
                  />
                </Popconfirm>
              ) : (
                <GradientButton
                  buttonText="Submit"
                  className="w-full"
                  loading={API?.isLoading}
                  disabled={!selectedTeams.size}
                  onClick={() => handleMediaWizard()}
                />
              )}

              <Button
                size="large"
                className="h-[48px] w-full"
                onClick={() => closeMediaWizard(false)}
              >
                Cancel
              </Button>
            </div>
          </>
        )}
        {mediaWizard === "delete" && (
          <div className="mobilescale">
            <div className="flex flex-col mb-[50px]">
              <div className="flex justify-center">
                <Image src={rightIcon} width={90} preview={false} />
              </div>
              <div className="text-4xl font-semibold text-center mt-[30px]">
                Media Deleted
              </div>
              <div className="text-lg text-[#6B7280] text-center mt-2">
                Unused media files were successfully deleted from all selected
                teams.
              </div>
            </div>
            <GradientButton
              className="w-full"
              buttonText="Go To Team Management"
              onClick={() => setMediaWizard(null)}
            />
          </div>
        )}
        {mediaWizard === "upload" && (
          <div className="mobilescale">
            <div className="flex flex-col mb-[50px]">
              <div className="flex justify-center">
                <Image src={rightIcon} width={90} preview={false} />
              </div>
              <div className="text-4xl font-semibold text-center mt-[30px]">
                Media Uploaded
              </div>
              <div className="text-lg text-[#6B7280] text-center mt-2">
                Media operation completed successfully, and all selected teams
                were updated.
              </div>
            </div>
            <GradientButton
              className="w-full"
              buttonText="Go To Team Management"
              onClick={() => setMediaWizard(null)}
            />
          </div>
        )}
        {mediaWizard === "uploadError" && (
          <>
            <div className="flex items-center justify-between">
              <div className="sm:text-3xl text-xl font-semibold text-[#020817]">
                Some Media Failed to Upload
              </div>
              <IoCloseOutline
                size={32}
                color="#9ca8bc"
                className="cursor-pointer mt-[6px]"
                onClick={() => {
                  closeMediaWizard(null);
                }}
              />
            </div>
            <div className="text-sm font-normal text-[#6b7280] md:mb-10 mb-6 pe-10">
              We couldn’t upload all media files to every team. See the details
              below.
            </div>
            <div className="w-full h-[400px] overflow-y-auto pr-2 custom-scroll">
              {Object.keys(failedImage || {})?.map((team) => {
                const files = failedImage[team];
                return (
                  <section className="w-full mt-5 max-md:max-w-full" key={team}>
                    <h2 className="text-[#020817] text-lg font-medium max-md:max-w-full">
                      {team}
                    </h2>
                    <div
                      role="list"
                      className="flex w-full items-center gap-2.5 text-sm font-normal flex-wrap mt-[7px] max-md:max-w-full"
                    >
                      {files.map((file, index) => (
                        <div
                          className="text-[#020817] self-stretch gap-2.5 bg-[#F6F7F9] my-auto p-2.5 rounded-[5px] text-sm font-normal"
                          role="listitem"
                          key={`team-${index}`}
                        >
                          {file?.fileName}
                        </div>
                      ))}
                    </div>
                  </section>
                );
              })}
            </div>
            <GradientButton
              className="px-14 mt-5"
              buttonText="Okay"
              onClick={() => {
                closeMediaWizard(null);
              }}
            />
          </>
        )}
      </Modal>
      <MediaPreview open={openPreview} setOpen={setOpenPreview} />
    </>
  );
};

export default TeamManagement;
