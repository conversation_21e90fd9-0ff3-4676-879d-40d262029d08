/* eslint-disable object-curly-newline */
import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer, Form, Image, Input } from "antd";
import React, { useState } from "react";
import { CloseOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import org from "../../../asset/logos/org.svg";
import use from "../../../asset/logos/use.svg";
import GradientButton from "../../common/GradientButton";
import useHttp from "../../../hooks/use-http";
import CONSTANTS from "../../../util/constant/CONSTANTS";

const RightDrawer = ({
  user,
  visible = false,
  setVisible = () => {},
  setRefresh,
}) => {
  const navigate = useNavigate();
  const API = useHttp();
  const [isEdit, setIsEdit] = useState(false);

  const onClose = () => {
    setIsEdit(false);
    setVisible(false);
  };
  const handleLogout = () => {
    // deleteAuthDetails();
    localStorage.clear();
    // window.location.reload();
    navigate("/");
  };

  const onUpdate = (value) => {
    const payload = {
      name: value?.name?.trim(),
      organizationName: value?.organizationName?.trim(),
    };
    API.sendRequest(
      CONSTANTS.API.auth.updateMe,
      (res) => {
        setRefresh((pre) => !pre);
        setIsEdit((pre) => !pre);
      },
      payload,
      "User Updated Successfully."
    );
  };

  return (
    <Drawer
      title={
        <span className="font-semibold text-3xl">
          {isEdit ? "Personal Info" : "Profile"}
        </span>
      }
      placement="right"
      onClose={onClose}
      open={visible}
      closeIcon={false}
      style={{ zoom: 0.9 }}
      width={450}
      extra={
        <CloseOutlined onClick={onClose} className="text-lg cursor-pointer" />
      }
    >
      {!isEdit && (
        <div className="flex-x center text-center profile-drawer">
          <div>
            <Avatar
              size={80}
              className="mt-8 bg-[#374151] shadow-lg"
              shape="circle"
            >
              <div className="font-semibold text-4xl">
                {user?.name?.charAt(0).toUpperCase()}
              </div>
            </Avatar>
            <div className="mt-5 text-2xl font-semibold text-[#020817] break-words">
              {user?.name}
            </div>
            <div className="text-base text-[#6B7280] mt-2 mb-12 break-words">
              {user?.email}
            </div>
            <GradientButton
              buttonText="Manage Your Account"
              className="w-72 mx-auto"
              onClick={() => setIsEdit((pre) => !pre)}
            />
            <Button danger className="mt-3 w-72 mx-auto" onClick={handleLogout}>
              Logout
            </Button>
          </div>
        </div>
      )}
      {isEdit && (
        <Form
          name="signup_form"
          className="signup-form px-8"
          initialValues={user}
          onFinish={onUpdate}
          validateTrigger="onBlur"
        >
          {/* Name */}
          <p className="font-medium text-base text-[#020817] required-field mb-2">
            Name<span className="text-red-600 ml-1">*</span>
          </p>
          <Form.Item
            className="mb-4"
            name="name"
            rules={[
              { required: true, message: "Please Enter Your Name!" },
              {
                whitespace: true,
                message: "Input cannot be empty or spaces only",
              },
              {
                max: 80,
                message: "Name cannot exceed 80 characters!",
              },
            ]}
          >
            <Input
              prefix={
                <Image src={use} alt="User Icon" preview={false} width={22} />
              }
              placeholder="enter name"
              autoComplete="off"
            />
          </Form.Item>

          {/* Organization */}
          <p className="font-medium text-base text-[#020817] required-field mb-2">
            Organization
          </p>
          <Form.Item
            className="mb-0"
            name="organizationName"
            rules={[
              {
                whitespace: true,
                message: "Input cannot be empty or spaces only",
              },
              {
                max: 80,
                message: "Name cannot exceed 80 characters!",
              },
            ]}
          >
            <Input
              prefix={
                <Image
                  src={org}
                  alt="Organization Icon"
                  preview={false}
                  width={22}
                />
              }
              placeholder="enter organization name"
              autoComplete="off"
            />
          </Form.Item>

          {/* Submit Button */}
          <Form.Item className="mt-12 mb-3">
            <GradientButton
              className="w-full"
              htmlType="submit"
              buttonText="Save"
              loading={API?.isLoading}
            />
          </Form.Item>
          <Button
            danger
            className="mx-auto w-full"
            onClick={() => setIsEdit((pre) => !pre)}
          >
            Cancel
          </Button>
        </Form>
      )}
    </Drawer>
  );
};

export default RightDrawer;
