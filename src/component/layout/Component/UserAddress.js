import { Button, Form, Input, Modal, Select } from "antd";
import { IoCloseOutline } from "react-icons/io5";
import GradientButton from "../../common/GradientButton";
import useHttp from "../../../hooks/use-http";
import CONSTANTS from "../../../util/constant/CONSTANTS";

const UserAddress = ({ open, setOpen }) => {
  const API = useHttp();
  const [form] = Form.useForm();

  const onBuyPlan = (value) => {
    const payload = {
      planId: open,
      city: value?.city?.trim() || "",
      state: value?.state?.trim() || "",
      country: value?.country?.trim(),
      street: value?.street?.trim() || "",
      zipcode: value?.zipcode?.trim() || "",
      return_url: window.location.origin,
      discount_code: value?.discount_code?.trim(),
    };
    API.sendRequest(
      CONSTANTS.API.payment.requestForPaymentLink,
      (res) => {
        if (res?.data?.payment_link)
          window.location.href = res?.data?.payment_link;
      },
      payload
    );
  };

  const handleChange = (field) => {
    const nameError = form.getFieldError(field);
    if (nameError.length) {
      form.validateFields([field]);
    }
  };

  return (
    <>
      <Modal
        open={open}
        closeIcon={false}
        footer={null}
        centered
        onCancel={() => {
          form.resetFields();
          setOpen(null);
        }}
        maskClosable={false}
        width={550}
        style={{ zoom: 0.9 }}
        className="rounded-[20px] md:rounded-[40px] overflow-hidden p-0 auth-modal"
        styles={{
          content: { padding: 0 },
          body: { padding: "50px" },
          mask: { backgroundColor: "rgba(0, 0, 0, 0.8)" },
        }}
      >
        <>
          <div className="flex items-center justify-between">
            <div className="sm:text-3xl text-2xl font-semibold text-[#020817]">
              Set Up Your Location
            </div>
            <IoCloseOutline
              size={32}
              color="#9ca8bc"
              className="cursor-pointer mt-[6px]"
              onClick={() => {
                form.resetFields();
                setOpen(null);
              }}
            />
          </div>
          <div className="text-sm font-normal text-[#6b7280] mb-10 pe-10">
            We just need a few more details to get things ready.
          </div>
          <Form
            name="team_m"
            onFinish={onBuyPlan}
            form={form}
            validateTrigger="onBlur"
          >
            {/* City */}
            <p className="font-medium text-base text-[#020817] required-field mb-2">
              City
            </p>
            <Form.Item
              className="mb-4"
              name="city"
              rules={[
                { required: false, message: "Please enter your city!" },
                {
                  whitespace: true,
                  message: "Input cannot be empty or spaces only",
                },
                {
                  max: 80,
                },
              ]}
            >
              <Input
                placeholder="enter city"
                autoComplete="off"
                onChange={() => handleChange("city")}
              />
            </Form.Item>

            {/* State */}
            <p className="font-medium text-base text-[#020817] required-field mb-2">
              State
            </p>
            <Form.Item
              className="mb-4"
              name="state"
              rules={[
                { required: false, message: "Please enter your state!" },
                {
                  whitespace: true,
                  message: "Input cannot be empty or spaces only",
                },
                {
                  max: 80,
                },
              ]}
            >
              <Input
                placeholder="enter state"
                autoComplete="off"
                onChange={() => handleChange("state")}
              />
            </Form.Item>

            {/* Country */}
            <p className="font-medium text-base text-[#020817] required-field mb-2">
              Country<span className="text-red-600 ml-1">*</span>
            </p>
            <Form.Item
              className="mb-4"
              name="country"
              rules={[
                { required: true, message: "Please select your country!" },
              ]}
            >
              <Select
                showSearch
                placeholder="select country"
                optionFilterProp="children"
                onChange={() => handleChange("country")}
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                options={CONSTANTS.countries}
              />
            </Form.Item>

            {/* Zipcode */}
            <p className="font-medium text-base text-[#020817] required-field mb-2">
              Zip Code
            </p>
            <Form.Item
              className="mb-4"
              name="zipcode"
              rules={[
                { required: false, message: "Please enter your zip code!" },
                {
                  pattern: /^\d{4,10}$/,
                  message: "Enter a valid zip code (4-10 digits)",
                },
                {
                  max: 80,
                },
              ]}
            >
              <Input
                type="number"
                placeholder="enter zip code"
                onChange={() => handleChange("zipcode")}
              />
            </Form.Item>

            <p className="font-medium text-base text-[#020817] required-field mb-2">
              Discount Code
            </p>
            <Form.Item
              className="mb-4"
              name="discount_code"
              rules={[
                {
                  required: false,
                  message: "Please enter your discount code!",
                },
                {
                  pattern: /^[A-Za-z0-9]+$/,
                  message: "Only letters and numbers allowed, no spaces",
                },
                {
                  max: 50,
                  message: "Discount code must be 50 characters or less",
                },
              ]}
            >
              <Input
                placeholder="Enter discount code"
                onChange={() => handleChange("discount_code")}
              />
            </Form.Item>

            {/* Buttons */}
            <Form.Item className="mt-[50px] mb-0">
              <div className="flex gap-4">
                <GradientButton
                  htmlType="submit"
                  className="w-1/2"
                  buttonText={"Submit"}
                  loading={API?.isLoading}
                />
                <Button
                  className="w-1/2"
                  onClick={() => {
                    form.resetFields();
                    setOpen(null);
                  }}
                >
                  Cancel
                </Button>
              </div>
            </Form.Item>
          </Form>
        </>
      </Modal>
    </>
  );
};

export default UserAddress;
