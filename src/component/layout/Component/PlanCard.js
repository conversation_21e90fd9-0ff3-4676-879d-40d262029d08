import React from "react";
import GradientButton from "../../common/GradientButton";
import { CircleCheck } from "lucide-react";
import { Avatar } from "antd";
import { basicPlan, bestPlan, boostPlan } from "../../../util/image";

export const PricingCard = ({ plan, onUpgrade = () => {} }) => {
  return (
    <div
      className={`w-full sm:w-[380px] flex flex-col items-start p-10 rounded-[30px] max-sm:p-[30px] hover:border-2 hover:border-[#a855f7] border-solid hover:bg-[#faf5ff] border-transparent bg-white`}
    >
      <div className="flex flex-col items-start gap-[20px] w-full">
        <div className="flex items-center justify-between w-full">
          <div className="w-[60px] h-[60px]">
            <Avatar
              src={
                plan?.name === "Lite"
                  ? basicPlan
                  : plan?.name === "Pro"
                  ? bestPlan
                  : boostPlan
              }
              className="w-16 h-16"
            />
          </div>
          {plan?.name !== "Ultra Boost" && (
            <div className="border border-purple-500 text-purple-500 text-xs px-2.5 py-1.5 rounded-[30px] border-solid">
              {plan?.name === "Lite" ? "Most Popular" : "Best Deal"}
            </div>
          )}
        </div>
        <div className="text-xl font-medium text-black max-sm:text-lg">
          {plan?.name}
        </div>
        <div className="text-4xl font-semibold text-black max-sm:text-3xl">
          ${plan?.price / 100}
        </div>
      </div>
      <div className="w-full h-px bg-[#D9DBDF] my-[30px]" />
      <div className="flex flex-col gap-5 max-sm:gap-[15px]">
        <div className="flex items-center gap-2.5 text-base text-gray-600 max-sm:text-sm">
          <CircleCheck color={"#EC4899"} size={20} />
          <div>
            {plan?.isTeamUnlimited ? "Unlimited" : plan?.teamLimit}{" "}
            {plan?.name === "Lite" ? "Team" : "Teams"}
          </div>
        </div>
        <div className="flex items-center gap-2.5 text-base text-gray-600 max-sm:text-sm">
          <CircleCheck color={"#EC4899"} size={20} />
          <div>
            {plan?.isPostUnlimited ? "Unlimited" : plan?.postLimit} Posts
          </div>
        </div>
      </div>
      <GradientButton
        onClick={onUpgrade}
        className="w-full mt-[50px]"
        buttonText={"Get Started"}
      />
    </div>
  );
};
