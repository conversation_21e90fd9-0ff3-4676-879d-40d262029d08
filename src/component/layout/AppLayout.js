import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Avatar, Card, Col, Image, Layout, Progress, Row, Tooltip } from "antd";
import moment from "moment";
import { IoArrowForward } from "react-icons/io5";
import RightDrawer from "./Component/right-drawer";
import CONSTANTS, { appRoot, loginRoot } from "../../util/constant/CONSTANTS";
import { getAuthToken } from "../../util/API/authStorage";
import logo from "../../asset/logos/icon.svg";
import king from "../../asset/image/king.svg";
import plain from "../../asset/image/plain.svg";
import planin from "../../asset/image/planin.svg";
import recip from "../../asset/image/Icons.svg";
import GradientButton from "../common/GradientButton";
import TeamManagement from "./TeamManagement";
import useHttp from "../../hooks/use-http";
import { Plane, Rocket } from "lucide-react";

const { Header, Content } = Layout;

const AppLayout = (props) => {
  const API = useHttp();
  const { user_details } = props;
  const subscriptionPlan = props?.user_details?.plan;
  const [analytics, setAnalytics] = useState({});
  const formattedName =
    user_details?.name?.charAt(0).toUpperCase() +
    user_details?.name?.slice(1).toLowerCase();

  const navigate = useNavigate();
  const [visible, setVisible] = useState(false);

  const getPostCount = () => {
    API.sendRequest(CONSTANTS.API.post.getPostCount, (res) => {
      setAnalytics(res);
    });
  };

  useEffect(() => {
    const isLogin = getAuthToken() !== undefined && getAuthToken() !== null;
    if (!isLogin) {
      navigate(loginRoot);
      return;
    }
    getPostCount();
  }, []);

  const getBackDate = (date, type = "Month", amount = 1) => {
    const formattedDate = moment(date)
      .subtract(amount, type?.toLowerCase())
      .format("DD/MM/YYYY");
    return formattedDate;
  };

  const formatToLocalDate = (dateString) =>
    moment(dateString).local().format("DD/MM/YYYY");

  return (
    <>
      <Layout
        hasSider
        style={{
          minHeight: "100vh",
          zoom: 0.9,
        }}
        className="site-layout w-full"
      >
        <Layout>
          <Header
            className="px-0 top-0 sticky z-10 h-[70px]"
            style={{
              backgroundColor: "#FFFFFF",
            }}
          >
            <Row
              align="middle"
              justify="space-between"
              className="mx-5 md:mx-24"
            >
              <Col span={10} md={6} className="center flex">
                <Image
                  style={{
                    cursor: "pointer",
                    objectFit: "contain",
                  }}
                  className="!w-44 md:!w-48"
                  preview={false}
                  src={logo}
                  onClick={() => {
                    navigate(`${appRoot}`);
                  }}
                />
              </Col>

              <Col
                span={14}
                md={10}
                style={{
                  display: "flex",
                  justifyContent: "end",
                  alignItems: "center",
                }}
              >
                {/* <Tooltip className="cursor-pointer" title="Usage">
                  <Bell
                    strokeWidth={1.5}
                    size={24}
                    className="mr-5 cursor-pointer"
                    color="#6b7280"
                  />
                </Tooltip> */}
                <Tooltip className="cursor-pointer" title="Billing Detail">
                  <Avatar
                    src={recip}
                    className="mr-5 cursor-pointer"
                    size={28}
                    shape="square"
                    onClick={() => navigate("/billing")}
                  />
                </Tooltip>

                <Avatar
                  size={36}
                  className="bg-[#374151] shadow-lg cursor-pointer"
                  shape="circle"
                  onClick={() => {
                    setVisible(true);
                  }}
                >
                  <div className="font-semibold text-sm">
                    {user_details?.name?.charAt(0).toUpperCase()}
                  </div>
                </Avatar>
              </Col>
            </Row>
          </Header>
          <Content className="sm:py-[30px] sm:px-[90px] p-[20px]">
            <Card
              className="rounded-[30px] mobilescale"
              styles={{ header: { padding: 30 }, body: { padding: 30 } }}
              title={
                <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-3">
                  <div className="flex gap-4 items-center lg:justify-between justify-center">
                    <div className="text-2xl lg:text-3xl font-semibold flex max-w-[calc(100%-120px)] lg:max-w-full">
                      <div className="truncate lg:max-w-md">
                        {formattedName}
                      </div>
                      ’s Team
                    </div>
                    <GradientButton
                      shape={"round"}
                      className="h-9 !px-3 max-w-[130px]"
                      child={
                        <>
                          <div className="flex items-center gap-2 justify-between">
                            <div className="w-5 flex items-center">
                              {subscriptionPlan?.name === "Lite" ? (
                                <Image
                                  src={planin}
                                  width={20}
                                  preview={false}
                                />
                              ) : subscriptionPlan?.name === "Pro" ? (
                                <Plane
                                  color="#fff"
                                  size={24}
                                  strokeWidth={1.5}
                                />
                              ) : (
                                <Rocket
                                  strokeWidth={1.5}
                                  color="#fff"
                                  size={24}
                                />
                              )}
                            </div>
                            <span className="text-white text-sm font-medium">
                              {subscriptionPlan?.name}
                            </span>
                          </div>
                        </>
                      }
                    />
                  </div>
                  <span className="text-[#6b7280] flex font-normal text-base lg:justify-between justify-center">
                    Plan period (
                    {getBackDate(
                      subscriptionPlan?.next_billing_date,
                      subscriptionPlan?.payment_frequency_interval,
                      subscriptionPlan?.payment_frequency_count
                    )}{" "}
                    to {formatToLocalDate(subscriptionPlan?.next_billing_date)})
                  </span>
                </div>
              }
            >
              <Row gutter={[24, 24]}>
                <Col span={10} xs={24} md={12} lg={8} xl={8} xxl={5}>
                  <Card
                    styles={{ body: { padding: 20 } }}
                    className="bg-[#faf5ff] border-0"
                  >
                    {analytics?.isPostUnlimited ? (
                      <div className="flex justify-between items-center h-full py-[14px]">
                        <div className="text-lg font-semibold">Posts</div>
                        <div className="text-lg font-semibold">
                          Unlimited 🥳
                        </div>
                      </div>
                    ) : (
                      <div className="py-1">
                        <div className="flex justify-between items-center mb-3">
                          <div className="text-lg font-semibold">Posts</div>
                          <div className="text-lg">
                            <span className="font-semibold">
                              {analytics?.postCount}
                            </span>
                            /{analytics?.postLimit}
                          </div>
                        </div>
                        <Progress
                          percent={
                            (analytics?.postCount * 100) / analytics?.postLimit
                          }
                          showInfo={false}
                          className="flex mt-5 sm:mt-0"
                          strokeColor="#a855f7"
                        />
                      </div>
                    )}
                  </Card>
                </Col>
                <Col span={10} xs={24} md={12} lg={8} xl={8} xxl={5}>
                  <Card
                    styles={{ body: { padding: "20px", height: "100%" } }}
                    className="bg-[#fdf2f8] border-0 h-full"
                  >
                    {analytics?.isTeamUnlimited ? (
                      <div className="flex justify-between items-center h-full py-[14px]">
                        <div className="text-lg font-semibold">Teams</div>
                        <div className="text-lg font-semibold">
                          Unlimited 🥳
                        </div>
                      </div>
                    ) : (
                      <div className="py-1">
                        <div className="flex justify-between items-center mb-3">
                          <div className="text-lg font-semibold">Teams</div>
                          <div className="text-lg">
                            <span className="font-semibold">
                              {analytics?.teamCount}
                            </span>
                            /{analytics?.teamLimit}
                          </div>
                        </div>
                        <Progress
                          percent={
                            (analytics?.teamCount * 100) / analytics?.teamLimit
                          }
                          showInfo={false}
                          className="flex mt-5 sm:mt-0"
                          strokeColor="#a855f7"
                        />
                      </div>
                    )}
                  </Card>
                </Col>
                {/* <Col span={10} xs={24} md={12} lg={10} xl={9} xxl={6}>
                  <GradientButton
                    className="sm:h-[88px] h-[100px] w-full go-unlimited px-3 sm:px-5"
                    child={
                      <>
                        <div className="flex items-center justify-between">
                          <div className="flex sm:flex-row flex-col items-start sm:items-center gap-2">
                            <div className="w-10">
                              <Avatar src={king} size={40} />
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-white text-sm font-semibold">
                                Go Unlimited – Post Everywhere!
                              </span>
                              <IoArrowForward color="#fff" size={16} />
                            </div>
                          </div>
                          <Image src={plain} preview={false} />
                        </div>
                      </>
                    }
                  />
                </Col> */}
              </Row>
            </Card>
            {user_details?.id && (
              <TeamManagement
                isSpecialUser={process.env.REACT_APP_SPECIAL_USER_IDS?.split(
                  ","
                )?.includes(user_details?.id?.toString())}
                isNeedMediaWizard={process.env.REACT_APP_MEDIA_WIZARD_USER_IDS?.split(
                  ","
                )?.includes(user_details?.id?.toString())}
              />
            )}
          </Content>
        </Layout>
      </Layout>
      <RightDrawer
        user={user_details}
        visible={visible}
        setVisible={setVisible}
        setRefresh={props?.setRefresh}
      />
    </>
  );
};

export default AppLayout;
