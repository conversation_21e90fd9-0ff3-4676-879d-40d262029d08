/* eslint-disable react/react-in-jsx-scope */
import { Navigate } from "react-router-dom";
import Error from "../page/error";
import UnAuthorize from "../page/unAuthorize";
import LogIn from "../page/user/login";
import AppLayout from "../component/layout/AppLayout";
import { appRoot, billingRoot, bulkpostRoot, teamRoot } from "./constant/CONSTANTS";
import NoInternet from "../page/noInternet";
import BlockUser from "../page/blockUser";
import MaintenancePage from "../page/maintenance";
import SuspendentUser from "../page/suspended";
import Dashboard from "../page/app/dashboard";
import Registration from "../page/user/Registration";
import TeamLayout from "../component/layout/TeamLayout";
import Settings from "../page/app/settings";
import BillingSubscriptions from "../page/app/billing";
import Mediamanagement from "../page/app/mediamanagement";
import { WritePostProvider } from "../page/app/dashboard/WritePostContext";
import Posts from "../page/app/posts";
import PostDetails from "../page/app/posts/PostDetails";
import { ViewPostProvider } from "../page/app/posts/WritePostContext";
import BillingLayout from "../component/layout/BillingLayout";
import BulkPost from "../page/app/bulkpost";

export const ROUTES = {
  dashboard: "/dashboard",
};

const LOGIN_ROUTES = [
  {
    index: true,
    element: <LogIn />,
  },
  {
    path: "/registration",
    element: <Registration />,
  },
];

const ALL_ROUTES = (appProps) => [
  ...LOGIN_ROUTES,
  {
    path: `${appRoot}`,
    element: <AppLayout {...appProps} />,
    index: true,
  },
  {
    path: `${billingRoot}`,
    element: <BillingLayout {...appProps} />,
    index: true,
  },
  {
    path: `${bulkpostRoot}`,
    element: <BulkPost {...appProps} />,
    index: true,
  },
  {
    path: `${teamRoot}`,
    element: <TeamLayout {...appProps} />,
    children: [
      {
        index: true,
        element: <Navigate to="/team" {...appProps} />,
      },
      {
        path: ":teamId", // Child route with a dynamic parameter
        children: [
          {
            index: true, // Default child route for /service/:id
            element: (
              <WritePostProvider>
                <Dashboard {...appProps} />
              </WritePostProvider>
            ),
          },
          {
            path: "media", // Child route for /service/:id/log
            element: <Mediamanagement {...appProps} />,
          },
          {
            path: "posts",
            element: <Posts {...appProps} />,
          },
          {
            path: "posts/:postId",
            element: (
              <ViewPostProvider>
                <PostDetails {...appProps} />
              </ViewPostProvider>
            ),
          },
          // {
          //   path: "billing", // Child route for /service/:id/log
          //   element: <BillingSubscriptions {...appProps} />,
          // },
          {
            path: "settings/:media",
            element: <Settings {...appProps} />,
          },
        ],
      },
    ],
  },
  {
    path: "/error",
    element: <Error />,
  },
  {
    path: "/unAuthorize",
    element: <UnAuthorize />,
  },
  {
    path: "/noInternet",
    element: <NoInternet />,
  },
  {
    path: "/undermaintenance",
    element: <MaintenancePage />,
  },
  {
    path: "*",
    element: <Navigate to="/error" />,
  },
];

export default ALL_ROUTES;
