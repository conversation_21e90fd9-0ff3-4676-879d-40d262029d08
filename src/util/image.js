import insta from "../asset/logos/insta.svg";
import fb from "../asset/logos/fb.svg";
import ld from "../asset/logos/ld.svg";
import ma from "../asset/logos/ma.svg";
import pro from "../asset/logos/pro.svg";
import prop from "../asset/logos/prop.svg";
import thre from "../asset/logos/thre.svg";
import Group21 from "../asset/logos/Group 21.svg";
import tik from "../asset/logos/tik.svg";
import x from "../asset/logos/x.svg";
import yt from "../asset/logos/yt.svg";
import Bplain from "../asset/image/fillplain.svg";
import boost from "../asset/image/icons/boost.svg";
import best from "../asset/image/icons/best.svg";
import basic from "../asset/image/icons/basic.svg";
import recipe from "../asset/image/icons/Menu Icons.svg";
import mdelete from "../asset/image/icons/Group 21.svg";

export const socialMediaIcons = {
  INSTAGRAM: insta,
  FACEBOOK: fb,
  LINKEDIN: ld,
  MASTODON: ma,
  PINTEREST: pro,
  REDDIT: prop,
  THREADS: thre,
  TIKTOK: tik,
  TWITTER: x,
  YOUTUBE: yt,
};

export const socialMedia = [
  { name: "TIKTOK", image: socialMediaIcons.TIKTOK },
  { name: "YOUTUBE", image: socialMediaIcons.YOUTUBE },
  { name: "INSTAGRAM", image: socialMediaIcons.INSTAGRAM },
  { name: "FACEBOOK", image: socialMediaIcons.FACEBOOK },
  { name: "TWITTER", image: socialMediaIcons.TWITTER },
  { name: "THREADS", image: socialMediaIcons.THREADS },
  { name: "LINKEDIN", image: socialMediaIcons.LINKEDIN },
  { name: "PINTEREST", image: socialMediaIcons.PINTEREST },
  { name: "REDDIT", image: socialMediaIcons.REDDIT },
  { name: "MASTODON", image: socialMediaIcons.MASTODON },
];

export const socialMediaIColor = {
  INSTAGRAM: "#ffe9f8",
  FACEBOOK: "#def3ff",
  LINKEDIN: "#e9f7ff",
  MASTODON: "#e2e1fe",
  PINTEREST: "#ffe3e7",
  REDDIT: "#feeae6",
  THREADS: "#eeeeee",
  TIKTOK: "#f4f1f1",
  TWITTER: "#e4e4e4",
  YOUTUBE: "#ffe9e9",
};

export const filplain = Bplain;
export const billingRecipe = recipe;
export const mediaDelete = mdelete;
export const apiKeyImage = Group21;

export const manualPostJason = {
  TWITTER: {
    text: "",
    uploadIds: [],
  },
  PINTEREST: {
    text: "",
    description: "",
    boardName: "",
    uploadIds: [],
  },
  FACEBOOK: {
    type: "POST",
    text: "",
    uploadIds: [],
  },
  INSTAGRAM: {
    type: "POST",
    text: "",
    uploadIds: [],
  },
  THREADS: {
    text: "",
    uploadIds: [],
  },
  TIKTOK: {
    text: "",
    uploadIds: [],
  },
  LINKEDIN: {
    text: "",
    uploadIds: [],
  },
  YOUTUBE: {
    type: "VIDEO",
    uploadIds: [],
    text: "",
    description: "",
  },
  REDDIT: {
    text: "",
    description: "",
    sr: "",
    uploadIds: [],
  },
  MASTODON: {
    text: "",
    uploadIds: [],
  },
};

const postValidationRules = {
  YOUTUBE: {
    media: {
      allowed: 1,
      type: ["video"],
      required: true,
      mediaValidationMessage: "You must upload exactly 1 video for YouTube.",
      ratio: {
        video: [
          {
            minRatio: "1:3",
            maxRatio: "1:1",
          },
          {
            minRatio: "1:3",
            maxRatio: "3:1",
          },
        ],
      },
    },
    text: { max: 100, required: false },
    description: { max: 5000, required: false },
    required: ["media"],
    message: "A video is required to post on YouTube.",
  },
  TIKTOK: {
    media: {
      allowed: 1,
      type: ["video"],
      required: true,
      mediaValidationMessage: "You must upload exactly 1 video for Tiktok.",
      ratio: {
        video: {
          minRatio: "0.01",
          maxRatio: "10:6",
        },
        image: {
          minRatio: null,
          maxRatio: null,
        },
      },
    },
    text: { max: 150, required: false },
    required: ["media"],
    message: "A video is required to post on Tiktok.",
  },
  TWITTER: {
    media: {
      allowed: 4,
      type: ["video", "image"],
      required: false,
      mediaValidationMessage:
        "You can upload up to 4 images or videos on Twitter.",
      ratio: {
        video: {
          minRatio: "1:3",
          maxRatio: "3:1",
        },
        image: {
          minRatio: null,
          maxRatio: null,
        },
      },
    },
    text: { max: 280, required: false },
    required: [{ or: ["media", "text"] }],
    message: "Either media or text is required to post on Twitter.",
  },
  THREADS: {
    media: {
      allowed: 10,
      type: ["video", "image"],
      required: false,
      mediaValidationMessage:
        "You can upload up to 10 images or videos on Threads.",
      ratio: {
        video: {
          minRatio: "0.01",
          maxRatio: "1.91",
        },
        image: {
          minRatio: "0.01",
          maxRatio: "1.91",
        },
      },
    },
    text: { max: 500, required: false },
    required: [{ or: ["media", "text"] }],
    message: "Either media or text is required to post on Threads.",
  },
  LINKEDIN: {
    media: {
      allowed: { video: 1, image: 10 },
      type: ["video", "image"],
      required: false,
      mediaValidationMessage:
        "You can upload up to 10 images or 1 video on LinkedIn.",
      ratio: {
        video: {
          minRatio: "1:2.4",
          maxRatio: "2.4:1",
        },
        image: {
          minRatio: null,
          maxRatio: null,
        },
      },
    },
    text: { max: 1300, required: false },
    required: [{ or: ["media", "text"] }],
    message: "Either media or text is required to post on LinkedIn.",
  },
  MASTODON: {
    media: {
      allowed: { video: 1, image: 4 },
      type: ["video", "image"],
      required: false,
      mediaValidationMessage:
        "You can upload up to 4 images or 1 video on Mastodon.",
      ratio: {
        video: {
          minRatio: "1:3",
          maxRatio: "1:1",
        },
        image: {
          minRatio: null,
          maxRatio: null,
        },
      },
    },
    text: { max: 30000, required: false },
    required: [{ or: ["media", "text"] }],
    message: "Either media or text is required to post on Mastodon.",
  },
  FACEBOOK: {
    media: {
      allowed: { video: 1, image: 10 },
      type: ["video", "image"],
      required: false,
      mediaValidationMessage:
        "You can upload up to 10 images or 1 video on Facebook.",
      ratio: {
        video: {
          minRatio: "0.01",
          maxRatio: "1.91",
        },
        image: {
          minRatio: "0.01",
          maxRatio: "1.91",
        },
      },
    },
    text: { max: 50000, required: false },
    required: [{ or: ["media", "text"] }],
    message: "Either media or text is required to post on Facebook.",
  },
  INSTAGRAM: {
    media: {
      allowed: 10,
      type: ["video", "image"],
      required: true,
      mediaValidationMessage:
        "At least one media file is required for Instagram. You can upload up to 10 images or videos.",
      ratio: {
        video: {
          minRatio: "0.01",
          maxRatio: "10:1",
        },
        image: {
          minRatio: "4:5",
          maxRatio: "1.91:1",
        },
      },
    },
    text: { max: 2000, required: false },
    required: ["media"],
    message: "Media upload is mandatory for Instagram.",
  },
  PINTEREST: {
    boardName: { required: true },
    media: {
      allowed: 1,
      type: ["video", "image"],
      required: true,
      mediaValidationMessage: "One media file is required for Pinterest.",
      ratio: {
        video: {
          minRatio: "1:2",
          maxRatio: "1.91:1",
        },
        image: {
          minRatio: null,
          maxRatio: null,
        },
      },
    },
    text: { max: 100, required: true },
    description: { max: 800, required: false },
    required: ["boardName", "media", "text"],
    message: "Board name, media, and text are required to post on Pinterest.",
  },
  REDDIT: {
    sr: { required: true },
    media: {
      allowed: { video: 1, image: 10 },
      type: ["video", "image"],
      required: false,
      mediaValidationMessage:
        "You can upload up to 10 images or 1 video on Reddit.",
      ratio: {
        video: {
          minRatio: "9:16",
          maxRatio: "16:9",
        },
        image: {
          minRatio: "1:1",
          maxRatio: "1:1",
        },
      },
    },
    text: { max: 300, required: false },
    description: { max: 30000, required: false },
    required: ["sr", { or: ["media", "text"] }],
    message:
      "Subreddit is required, and either media or text must be provided for Reddit.",
  },
};

export const postRequiredSchema = {
  YOUTUBE: {
    required: ["media"],
  },
  TIKTOK: {
    required: ["media"],
  },
  TWITTER: {
    required: [{ or: ["media", "text"] }],
  },
  THREADS: {
    required: [{ or: ["media", "text"] }],
  },
  LINKEDIN: {
    required: [{ or: ["media", "text"] }],
  },
  MASTODON: {
    required: [{ or: ["media", "text"] }],
  },
  FACEBOOK: {
    required: [{ or: ["media", "text"] }],
  },
  INSTAGRAM: {
    required: ["media"],
  },
  PINTEREST: {
    required: ["media", "text"],
  },
  REDDIT: {
    required: [{ or: ["media", "text"] }],
  },
};

export default postValidationRules;

export const basicPlan = basic;
export const bestPlan = best;
export const boostPlan = boost;

export const pricingJason = [
  {
    icon: basic,
    badge: "Most Popular",
    title: "Basic",
    price: "3.99",
    feature: ["1 Teams", "100 Posts"],
  },
  {
    icon: best,
    badge: "Best Deal",
    title: "Pro",
    price: "9.99",
    feature: ["3 Teams", "Unlimited Posts"],
  },
  {
    icon: boost,
    badge: null,
    title: "Ultra Boost",
    price: "12.99",
    feature: ["Unlimited Teams", "Unlimited Posts"],
  },
];

export function getSocialProfileUrl(platform, username) {
  if (!platform || !username) return null;

  const urls = {
    INSTAGRAM: `https://instagram.com/${username}`,
    FACEBOOK: `https://facebook.com/${username}`,
    LINKEDIN: `https://linkedin.com/company/${username}`,
    // MASTODON: `https://${username}`, // Mastodon usernames usually include domain
    PINTEREST: `https://pinterest.com/${username}`,
    REDDIT: `https://www.reddit.com/user/${username}`,
    THREADS: `https://www.threads.net/@${username}`,
    TIKTOK: `https://www.tiktok.com/@${username}`,
    TWITTER: `https://twitter.com/${username}`,
    YOUTUBE: `https://www.youtube.com/@${username}`,
  };

  return urls[platform];
}
