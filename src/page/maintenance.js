import { Result } from "antd";
import React from "react";
import GradientButton from "../component/common/GradientButton";

const MaintenancePage = () => {
  return (
    <>
      <div className="w-full h-svh flex justify-center items-center">
        <div className="mobilescale">
          <Result
            status="warning"
            title="We Are Under Maintenance"
            subTitle="We're working hard to improve your experience. Please check back soon for updates!"
            extra={
              <GradientButton
                className="textcolor"
                onClick={() => window.location.reload()}
                buttonText="Refresh"
              />
            }
          />
        </div>
      </div>
    </>
  );
};

export default MaintenancePage;
