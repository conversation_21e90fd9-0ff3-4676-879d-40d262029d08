import {
  Avatar,
  Button,
  Card,
  Col,
  Form,
  Input,
  Row,
  Select,
  Spin,
} from "antd";
import React, { useEffect, useState } from "react";
import GradientButton from "../../../component/common/GradientButton";
import { getSocialProfileUrl, socialMedia } from "../../../util/image";
import { BookText, KeyRound, Link2, Link2Off } from "lucide-react";
import useHttp from "../../../hooks/use-http";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { apiGenerator } from "../../../util/functions";
import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import { LoadingOutlined } from "@ant-design/icons";
import ApikeyModal from "../../../component/common/ApikeyModal";
import MastodonUrl from "../../../component/common/MastodonUrl";
import ChnanalModal from "../../../component/common/ChnanalModal";

const companyInfo = {
  name: "CompanyInformation",
};

const Settings = () => {
  const API = useHttp();
  const [form] = Form.useForm();
  const { teamId, media } = useParams();
  const [searchParams] = useSearchParams();
  const successValue = searchParams.get("success")?.split("-")[0];
  const navigate = useNavigate();

  const [selectedM, setSelectedM] = useState(companyInfo);
  const [rules, setRules] = useState([]);
  const [openApiModal, setOpenApiModal] = useState(false);
  const [openUrlModal, setOpenUrlModal] = useState(false);
  const [boardName, setBoardname] = useState([]);
  const [openChannelModal, setOpenChannelModal] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [userNameList, setUserNameList] = useState({});

  useEffect(() => {
    if (!teamId) return;
    getUpdatedData();
  }, [teamId]);

  useEffect(() => {
    if (!media || !rules?.CompanyInformation) return;

    if (media === "company") {
      setSelectedM(companyInfo);
      form.setFieldsValue({
        rule: rules?.CompanyInformation?.businessInfo,
      });
    } else {
      const matched = socialMedia.find(
        (item) => item.name.toLowerCase() === media.toLowerCase()
      );
      if (matched) {
        setSelectedM(matched);
        form.setFieldsValue({
          rule: rules?.[matched.name]?.rule,
          ...(rules?.[matched.name]?.channel && {
            channel: rules?.[matched.name]?.channel,
          }),
        });
      }
    }
  }, [media, rules]);

  useEffect(() => {
    if (
      successValue &&
      ["youtube", "facebook", "linkedin", "instagram"].includes(successValue)
    )
      setOpenChannelModal(true);
  }, [successValue]);

  const onSelectChannel = () => {
    setOpenChannelModal(false);
    // navigate(`/team/${teamId}/settings/${successValue}`);
    window.location.href = `/team/${teamId}/settings/${successValue}`;
  };

  useEffect(() => {
    if (
      teamId &&
      selectedM?.name &&
      rules?.CompanyInformation?.socialAccounts?.includes(selectedM?.name)
    ) {
      if (["PINTEREST", "REDDIT"]?.includes(selectedM?.name)) {
        API.sendRequest(
          apiGenerator(
            CONSTANTS.API.post.getChannels,
            {},
            `?teamId=${teamId}&type=${selectedM?.name}`
          ),
          (res) => {
            setBoardname(res?.data);
          }
        );
      }
    }
  }, [selectedM?.name]);

  const getUpdatedData = () => {
    API.sendBulkRequest(
      [
        apiGenerator(CONSTANTS.API.teams.getOne, { teamId }),
        apiGenerator(CONSTANTS.API.mediaRule.getAll, { mId: teamId }),
      ],
      (res) => {
        const mergedSocials = Object.assign(
          {},
          ...res?.[0]?.data?.data?.userNames
        );
        setUserNameList(mergedSocials);
        const result = res?.[1]?.data?.data?.rows.reduce((acc, item) => {
          acc[item?.platform] = item;
          return acc;
        }, {});
        result["CompanyInformation"] = res?.[0]?.data?.data;
        setRules(result);
      }
    );
  };

  const updateOne = (value) => {
    if (!teamId) return;
    const payload =
      selectedM?.name === "CompanyInformation"
        ? { businessInfo: value?.rule }
        : {
            platform: selectedM?.name,
            rule: value?.rule,
            teamId,
            ...(["PINTEREST", "REDDIT"].includes(selectedM?.name)
              ? { channel: value?.channel }
              : {}),
          };
    const endpoint =
      selectedM?.name === "CompanyInformation"
        ? apiGenerator(CONSTANTS.API.teams.update, { teamId })
        : CONSTANTS.API.mediaRule.UpdateOne;
    API.sendRequest(
      endpoint,
      (res) => {
        selectedM?.name === "CompanyInformation"
          ? (rules["CompanyInformation"] = {
              ...rules?.CompanyInformation?.businessInfo,
              ...res?.data,
            })
          : (rules[selectedM?.name] = res?.data);
        setRules({ ...rules });
      },
      payload,
      selectedM?.name === "CompanyInformation"
        ? "Company information updated successfully."
        : `${
            selectedM.name.charAt(0).toUpperCase() +
            selectedM.name.slice(1).toLowerCase()
          } instructions saved successfully.`
    );
  };

  const disConnectMedia = () => {
    if (teamId && selectedM?.name)
      API?.sendRequest(
        apiGenerator(
          CONSTANTS.API.connectMedia.disconnect,
          {},
          `?teamId=${teamId}&type=${selectedM?.name}`
        ),
        () => {
          getUpdatedData();
        },
        {},
        "Social account disconnected successfully."
      );
  };

  const connectMedia = () => {
    if (selectedM?.name === "MASTODON") {
      setOpenUrlModal(true);
      return;
    }
    if (teamId && selectedM?.name)
      API?.sendRequest(
        CONSTANTS.API.connectMedia.connectOne,
        (res) => {
          if (res?.url) window.open(res?.url, "_blank");
        },
        {
          teamId: teamId,
          type: selectedM?.name,
          redirectUrl: window.location.href,
        }
      );
  };

  const connectDisconnectMedia = () => {
    rules?.CompanyInformation?.socialAccounts?.includes(selectedM?.name)
      ? disConnectMedia()
      : connectMedia();
  };

  const handleSearch = (value) => {
    setSearchValue(value);
  };

  const handleKeyDown = (e) => {
    if (selectedM?.name === "PINTEREST") {
      if (
        e.key === "Enter" &&
        searchValue &&
        !boardName.includes(searchValue)
      ) {
        const updatedBoardName = [...boardName, searchValue];
        setBoardname(updatedBoardName);
        form.setFieldsValue({ channel: searchValue });
        setSearchValue("");
      }
    }
  };

  return (
    <>
      <Spin
        spinning={API?.isLoading}
        indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
      >
        <Row className="w-full flex flex-col-reverse md:flex-row justify-between items-center flex-wrap gap-5 mb-8">
          <div className="flex flex-wrap items-center gap-4">
            <Avatar
              icon={
                <BookText
                  strokeWidth={1.5}
                  size={22}
                  color={
                    selectedM?.name === "CompanyInformation"
                      ? "#a855f7"
                      : "#6b7280"
                  }
                />
              }
              size={44}
              onClick={() => {
                form.setFieldsValue({
                  rule: rules?.CompanyInformation?.businessInfo,
                });
                setSelectedM(companyInfo);
                navigate(`/team/${teamId}/settings/company`);
              }}
              className={`${
                selectedM?.name === "CompanyInformation"
                  ? "border-[#a855f7] bg-[#faf5ff]"
                  : "border-[#d9dbdf]"
              } border-[1px] p-2 bg-transparent cursor-pointer`}
            />
            {socialMedia?.map((md) => (
              <Avatar
                key={md.name}
                src={md.image}
                size={44}
                onClick={() => {
                  form.setFieldsValue({
                    rule: rules?.[md?.name]?.rule,
                    ...(rules?.[md?.name]?.channel && {
                      channel: rules?.[md?.name]?.channel,
                    }),
                  });
                  setSelectedM(md);
                  navigate(
                    `/team/${teamId}/settings/${md?.name.toLowerCase()}`
                  );
                }}
                className={`${
                  selectedM?.name === md?.name
                    ? "border-[#a855f7] bg-[#faf5ff]"
                    : "border-[#d9dbdf]"
                } border-[1px] p-2 bg-transparent cursor-pointer`}
              />
            ))}
          </div>
          <Button
            variant="solid"
            htmlType="button"
            className="bg-[#ec4899] text-white hover:!bg-[#ec4899] hover:!text-white hover:shadow-none px-10 
               self-start md:self-auto"
            icon={<KeyRound strokeWidth={1.5} size={22} color="#fff" />}
            onClick={() => setOpenApiModal(true)}
          >
            Update API Key
          </Button>
        </Row>

        <Card
          className="w-full rounded-[30px]"
          styles={{ body: { padding: 30 } }}
        >
          <Row className="flex flex-col gap-[30px]">
            <div className="flex justify-between items-center flex-wrap gap-4">
              <div className="text-xl font-medium text-[#a855f7] capitalize">
                {selectedM?.name === "CompanyInformation"
                  ? "Company Information"
                  : selectedM?.name?.toLowerCase()}
              </div>
              {selectedM?.name !== "CompanyInformation" && (
                <div className="flex gap-2">
                  {console.log(
                    getSocialProfileUrl(
                      selectedM?.name,
                      userNameList[selectedM?.name]
                    )
                  )}
                  {getSocialProfileUrl(
                    selectedM?.name,
                    userNameList[selectedM?.name]
                  ) && (
                    <Button
                      onClick={() =>
                        window.open(
                          getSocialProfileUrl(
                            selectedM?.name,
                            userNameList[selectedM?.name]
                          ),
                          "_blank"
                        )
                      }
                    >
                      View Profile
                    </Button>
                  )}
                  <Button
                    variant={
                      rules?.CompanyInformation?.socialAccounts?.includes(
                        selectedM?.name
                      )
                        ? "solid"
                        : "outlined"
                    }
                    color="default"
                    className="px-10 font-medium"
                    icon={
                      rules?.CompanyInformation?.socialAccounts?.includes(
                        selectedM?.name
                      ) ? (
                        <Link2Off strokeWidth={1.5} size={22} />
                      ) : (
                        <Link2 strokeWidth={1.5} size={22} />
                      )
                    }
                    onClick={connectDisconnectMedia}
                  >
                    {rules?.CompanyInformation?.socialAccounts?.includes(
                      selectedM?.name
                    )
                      ? "Disconnect"
                      : "Connect"}
                  </Button>
                </div>
              )}
            </div>
            <Row span={24} className="w-full">
              <div className="text-base font-medium mb-2 w-full">
                {selectedM?.name === "CompanyInformation"
                  ? "Company Information"
                  : "Generation Instructions"}
              </div>
              <Form
                name="team_m"
                onFinish={updateOne}
                className="w-full"
                form={form}
                validateTrigger="onBlur"
              >
                <Form.Item
                  className="mb-0"
                  name="rule"
                  rules={[
                    { required: true, message: "Please Enter..." },
                    {
                      whitespace: true,
                      message: "Input cannot be empty or spaces only",
                    },
                  ]}
                >
                  <Input.TextArea
                    className="w-full rounded-[10px] p-5"
                    placeholder={
                      selectedM?.name === "CompanyInformation"
                        ? "enter company information"
                        : "enter your instructions"
                    }
                    autoSize={{ minRows: 6 }}
                  />
                </Form.Item>

                {["PINTEREST", "REDDIT"]?.includes(selectedM?.name) && (
                  <Form.Item
                    name={"channel"}
                    className="max-w-md"
                    rules={[
                      { required: false, message: "Please select a channel" },
                    ]}
                  >
                    <Select
                      className="w-full mt-4"
                      placeholder={`Select ${
                        selectedM?.name === "REDDIT" ? "sr" : "boardName"
                      }`}
                      disabled={
                        !rules?.CompanyInformation?.socialAccounts?.includes(
                          selectedM?.name
                        )
                      }
                      showSearch
                      onSearch={handleSearch}
                      onInputKeyDown={handleKeyDown}
                      filterOption={(input, option) =>
                        option.label.toLowerCase().includes(input.toLowerCase())
                      }
                      options={boardName.map((item) => ({
                        label: item,
                        value: item,
                      }))}
                    />
                  </Form.Item>
                )}

                <Col span={8} xs={24} sm={12} md={12} lg={10} xl={8} xxl={6}>
                  <Form.Item className="mb-0 mt-[30px]">
                    <GradientButton
                      className="w-full"
                      buttonText="Save Settings"
                      htmlType="submit"
                    />
                  </Form.Item>
                </Col>
              </Form>
            </Row>
          </Row>
        </Card>
      </Spin>
      <ApikeyModal
        openApiModal={openApiModal}
        setOpenApiModal={setOpenApiModal}
      />
      <MastodonUrl
        setOpenUrlModal={setOpenUrlModal}
        openUrlModal={openUrlModal}
      />
      <ChnanalModal
        openChannelModal={openChannelModal}
        platform={successValue}
        channelOpt={rules?.CompanyInformation?.channels}
        onSubmit={onSelectChannel}
        API={API}
      />
    </>
  );
};

export default Settings;
