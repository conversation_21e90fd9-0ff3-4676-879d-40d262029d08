import { <PERSON><PERSON>, <PERSON>, <PERSON>, Row } from "antd";
import React from "react";
import { filplain } from "../../../util/image";
import GradientButton from "../../../component/common/GradientButton";

const BillingSubscriptions = () => {
  return (
    <Card className="w-full rounded-[30px]" styles={{ body: { padding: 30 } }}>
      <Row className="w-full">
        <div className="text-xl font-normal w-full">
          ⏳ Stay Covered! Your subscription runs from{" "}
          <span className="font-semibold text-[#ec4899]">01/02/2025</span> to{" "}
          <span className="font-semibold text-[#ec4899]">01/05/2025</span>.
          Renew on time to keep enjoying seamless access! ✨
        </div>
        <Row className="w-full">
          <Col
            span={24}
            className="mb-[50px] mt-[30px]"
            xs={24}
            sm={24}
            md={18}
            lg={12}
            xl={10}
            xxl={8}
          >
            <Card
              className="rounded-[30px] ant-card-custom"
              styles={{ body: { padding: 30 } }}
            >
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-5">
                  <Avatar src={filplain} size={60} />
                  <div className="text-[#a855f7] text-xl font-medium">
                    Basic Plan
                  </div>
                </div>
                <div>
                  <span className="text-4xl font-medium me-2">$30</span>
                  <span className="text-base font-normal text-[#6b7280]">
                    /month
                  </span>
                </div>
              </div>
              <div className="font-normal text-sm text-[#9ca8bc] mt-[25px]">
                Lorem ipsum dolor sit amet consectetur. Egestas vel ut sit
                suspendisse sagittis id metus lorem. Integer vitae et id leo
                nisl malesuada et nunc.
              </div>
            </Card>
          </Col>
        </Row>
        <Col span={8} xs={24} sm={12} md={12} lg={10} xl={7} xxl={6}>
          <GradientButton className="w-full" buttonText="Manage Billing" />
        </Col>
      </Row>
    </Card>
  );
};

export default BillingSubscriptions;
