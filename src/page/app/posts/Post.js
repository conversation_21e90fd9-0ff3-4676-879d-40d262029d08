import { <PERSON><PERSON>, <PERSON>, Popconfirm, Row } from "antd";
import useHttp from "../../../hooks/use-http";
import { useNavigate, useParams } from "react-router-dom";
import { socialMediaIcons } from "../../../util/image";
import { Info, Trash2 } from "lucide-react";
import StatusBadge from "./StatusBadge";
import moment from "moment";
import { convertUTCToLocal } from "../../../util/functions";

const Post = ({ post = "", onDelete }) => {
  const { teamId } = useParams();
  const navigate = useNavigate();

  return (
    <>
      <Card
        styles={{ body: { padding: 30, height: "100%" } }}
        className="md:rounded-[30px] rounded-[20px] ant-card-custom h-[300px] cursor-pointer"
        onClick={(e) =>
          navigate(`/team/${teamId}/posts/${post?.socialBundlePostId}`)
        }
      >
        <div className="flex flex-col h-full justify-between">
          <Row justify="space-between" align="middle">
            <StatusBadge status={post?.status} />
            <div className="flex gap-1">
              {post?.socialAccounts?.map((ac) => (
                <Avatar src={socialMediaIcons?.[ac]} size={24} shape="square" />
              ))}{" "}
            </div>
          </Row>

          <div
            className={`text-base font-normal text-[#020817] line-clamp-2 ${
              post?.allUploadIdsInPost?.length
                ? "h-[48px] line-clamp-2"
                : "h-[143px] line-clamp-6"
            } `}
          >
            {post?.title}
          </div>

          {post?.allUploadIdsInPost?.length ? (
            <div
              className="flex gap-3 overflow-x-auto whitespace-nowrap custom-scrollbar"
              onClick={(e) => e.stopPropagation()}
            >
              {post?.allUploadIdsInPost?.map((url, index) => (
                <div className="relative group">
                  <img
                    src={url}
                    alt="Url"
                    className="object-cover w-[70px] h-[70px] rounded-md"
                  />
                </div>
              ))}
            </div>
          ) : (
            <></>
          )}

          <Row className="flex justify-between items-center">
            <div className="text-base font-normal text-[#6b7280]">
              {/* {moment.utc(post?.postDate).local().format("DD/MM/YYYY")} */}
              {convertUTCToLocal(post?.postDate, "D MMMM YYYY, hh:mm A")}
            </div>
            {post?.status !== "DELETED" && (
              <Popconfirm
                title={"Delete the post"}
                description={"Are you sure you want to delete this post?"}
                icon={
                  <Info
                    strokeWidth={1.5}
                    size={16}
                    color="red"
                    className="mt-[2px] me-1"
                  />
                }
                onConfirm={(e) => {
                  e.stopPropagation();
                  onDelete();
                }}
                onCancel={(e) => e.stopPropagation()}
                okText={"Yes"}
                cancelText={"No"}
              >
                <Trash2
                  size={23}
                  color="#6b7280"
                  strokeWidth={1.5}
                  className="cursor-pointer"
                  onClick={(e) => e.stopPropagation()}
                />
              </Popconfirm>
            )}
          </Row>
        </div>
      </Card>
    </>
  );
};

export default Post;
