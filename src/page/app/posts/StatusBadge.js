const statusStyles = {
  POSTED: {
    background: "#1FC16B1A",
    border: "1px solid #1FC16B",
    color: "#1FC16B",
  },
  ERROR: {
    background: "#FF77001A",
    border: "1px solid #FF7700",
    color: "#FF7700",
  },
  SCHEDULED: {
    background: "#279CFC1A",
    border: "1px solid #279CFC",
    color: "#279CFC",
  },
  PROCESSING: {
    background: "#F1BA041A",
    border: "1px solid #F1BA04",
    color: "#F1BA04",
  },
  DELETED: {
    background: "#FB37481A",
    border: "1px solid #FB3748",
    color: "#FB3748",
  },
  DRAFT: {
    background: "#4B55631A",
    border: "1px solid #4B5563",
    color: "#4B5563",
  },
};

const StatusBadge = ({ status = "" }) => {
  return (
    <div
      style={{
        borderRadius: "5px",
        fontSize: "12px",
        fontWeight: "500",
        padding: "8px 10px",
        display: "inline-block",
        lineHeight: "normal",
        ...statusStyles[status], // Apply dynamic styles
      }}
      className="capitalize"
    >
      {status?.toLowerCase()}
    </div>
  );
};

export default StatusBadge;
