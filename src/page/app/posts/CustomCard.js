import React, { useEffect, useState } from "react";
import {
  Card,
  Row,
  Button,
  Avatar,
  Popconfirm,
  Modal,
  Form,
  Input,
  Select,
  Popover,
  Tooltip,
} from "antd";
import {
  CircleCheck,
  CircleX,
  Eye,
  FilePenLine,
  Image,
  Info,
  Play,
  Trash2,
  TriangleAlert,
} from "lucide-react";
import postValidationRules, {
  socialMediaIColor,
  socialMediaIcons,
} from "../../../util/image";
import { IoCloseOutline } from "react-icons/io5";
import MediaPreview from "../../../component/common/MediaPreview";
import useHttp from "../../../hooks/use-http";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { apiGenerator } from "../../../util/functions";
import { useParams } from "react-router-dom";
import { useWritePost } from "./WritePostContext";
import MediaList from "./MediaList";
import moment from "moment";
import GradientButton from "../../../component/common/GradientButton";

const CustomCard = ({ card = "" }) => {
  const [form] = Form.useForm();
  const API = useHttp();
  const { teamId } = useParams();
  const { manualPost, setManualPost, fileList } = useWritePost();
  const [openMList, setOpenMList] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState(
    manualPost?.data?.[card]?.uploadIds || []
  );
  const [boardName, setBoardname] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  // const [isEdit, setIsEdit] = useState(false);
  const fields = manualPost?.data?.[card] || {};
  const hasDescription = "description" in fields;
  const hasSelect = ["REDDIT", "PINTEREST"]?.includes(card);
  const [open, setOpen] = useState(null);
  const content = Form.useWatch(hasDescription ? "description" : "text", form);
  const postedInProgres =
    "SCHEDULED" === manualPost?.status &&
    manualPost?.postDate &&
    moment(manualPost.postDate).isBefore(moment())
      ? "PROCESSING"
      : manualPost?.status;

  const [copied, setCopied] = useState(false);

  const handleCopyLink = (link) => {
    navigator.clipboard.writeText(link).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 700); // hide tooltip after 2 seconds
    });
  };

  useEffect(() => {
    form.setFieldsValue({ ...manualPost?.data?.[card] });
    if (teamId && card) {
      if (["PINTEREST", "REDDIT"]?.includes(card))
        API.sendRequest(
          apiGenerator(
            CONSTANTS.API.post.getChannels,
            {},
            `?teamId=${teamId}&type=${card}`
          ),
          (res) => {
            setBoardname(res?.data);
          }
        );
    }
  }, [card]);

  const handleSubmitClick = () => {
    form.submit();
  };

  const submitData = (value) => {
    const currentObj = { ...manualPost?.data?.[card], ...value };
    setManualPost((pre) => {
      return { ...pre, data: { ...pre.data, [card]: currentObj } };
    });
    // setIsEdit(false);
  };

  const onSelect = () => {
    const currentObj = {
      ...manualPost?.data?.[card],
      uploadIds: selectedMedia,
    };
    setManualPost((pre) => {
      return { ...pre, data: { ...pre.data, [card]: currentObj } };
    });

    // setSelectedMedia([]);
    setOpenMList(false);
  };

  const forSeleceOption = (value) => {
    if (!value) return;

    const currentObj = {
      ...manualPost?.data?.[card],
      [card === "REDDIT" ? "sr" : "boardName"]: value,
    };
    setManualPost((pre) => {
      return { ...pre, data: { ...pre.data, [card]: currentObj } };
    });
    setSearchValue("");
  };

  const onMediaDelete = (mid) => {
    if (mid) {
      const mediaIds = manualPost?.data?.[card]?.uploadIds?.filter(
        (id) => id !== +mid
      );
      setSelectedMedia(mediaIds);
      setManualPost((pre) => {
        return {
          ...pre,
          data: {
            ...pre.data,
            [card]: { ...pre?.data?.[card], uploadIds: mediaIds },
          },
        };
      });
    }
  };

  const onDelete = () => {
    setManualPost((pre) => {
      if (pre?.data?.[card]) {
        const updatedData = { ...pre?.data };
        delete updatedData[card];

        return { ...pre, data: updatedData };
      }
    });
  };

  const handleSearch = (value) => {
    setSearchValue(value);
  };

  const handleKeyDown = (e) => {
    if (card === "PINTEREST") {
      if (
        e.key === "Enter" &&
        searchValue &&
        !boardName.includes(searchValue)
      ) {
        forSeleceOption(searchValue);
        setBoardname([...boardName, searchValue]);
      }
    }
  };

  return (
    <>
      <Card
        styles={{ body: { padding: 30, height: "100%" } }}
        className="rounded-[30px] shadow-lg"
        style={{
          backgroundColor: socialMediaIColor?.[card],
          border: fields?.validation === false ? "1px solid red" : "none",
        }}
      >
        <div className="flex flex-col h-full justify-between gap-6">
          <Row justify="space-between" align="middle">
            <div className="flex gap-2 items-center">
              <Avatar src={socialMediaIcons?.[card]} size={24} shape="square" />
              <div className="text-base font-medium capitalize">
                {card?.toLowerCase()}
              </div>

              {fields?.validation === false && (
                <Popover content={postValidationRules?.[card].message}>
                  <TriangleAlert size={20} color="#fb3748" />
                </Popover>
              )}
            </div>
            {!["PROCESSING", "POSTED", "ERROR", "DELETED"].includes(
              postedInProgres
            ) && (
              <div className="flex items-center gap-3">
                {/* {isEdit ? (
                  <>
                    <CircleCheck
                      className="cursor-pointer text-[#1fc16b]"
                      size={25}
                      onClick={handleSubmitClick}
                    />
                    <Popconfirm
                      title="Cancel Update"
                      description="Are you sure you want to cancel updating this post?"
                      icon={
                        <Info
                          strokeWidth={1.5}
                          size={16}
                          color="red"
                          className="mt-[2px] me-1"
                        />
                      }
                      onConfirm={(e) => {
                        e.stopPropagation();
                        form.resetFields();
                        setIsEdit(false);
                      }}
                      onCancel={(e) => e.stopPropagation()}
                      okText={"Yes"}
                      cancelText={"No"}
                    >
                      <CircleX
                        strokeWidth={1.5}
                        size={25}
                        className="cursor-pointer text-red-500"
                      />
                    </Popconfirm>
                  </>
                ) : ( */}
                <>
                  {/* <FilePenLine
                      size={22}
                      className="cursor-pointer text-[#4b5563]"
                      onClick={() => {
                        form.setFieldsValue({ ...manualPost?.data?.[card] });
                        setIsEdit(true);
                      }}
                    /> */}
                  <Popconfirm
                    title={"Delete the post"}
                    description={"Are you sure you want to delete this post?"}
                    icon={
                      <Info
                        strokeWidth={1.5}
                        size={16}
                        color="red"
                        className="mt-[2px] me-1"
                      />
                    }
                    onConfirm={(e) => {
                      e.stopPropagation();
                      onDelete();
                    }}
                    onCancel={(e) => e.stopPropagation()}
                    okText={"Yes"}
                    cancelText={"No"}
                  >
                    <Trash2 size={22} className="cursor-pointer text-red-500" />
                  </Popconfirm>
                </>
                {/* )} */}
              </div>
            )}
            {["POSTED"].includes(postedInProgres) &&
              manualPost?.externalData?.[card]?.permalink && (
                <div className="flex items-center gap-3">
                  <Tooltip
                    title="Copied!"
                    open={copied}
                    placement="top"
                    color="#10b981"
                  >
                    <GradientButton
                      buttonText="Copy Link"
                      className="h-9 text-sm font-medium"
                      onClick={() =>
                        handleCopyLink(
                          manualPost?.externalData?.[card]?.permalink
                        )
                      }
                    />
                  </Tooltip>

                  <GradientButton
                    buttonText="View Post"
                    className="h-9 text-sm font-medium"
                    onClick={() =>
                      window.open(
                        manualPost?.externalData?.[card]?.permalink,
                        "_blank"
                      )
                    }
                  />
                </div>
              )}
          </Row>

          {/* {!isEdit ? (
            <div>
              {hasDescription &&
                (fields?.text ? (
                  <div
                    className={`text-base font-medium line-clamp-1 ${
                      hasDescription ? "mt-[14px]" : ""
                    }`}
                  >
                    {fields?.text}
                  </div>
                ) : (
                  <div className="text-base font-normal text-[#6b7280] line-clamp-1">
                    Start typing your title...
                  </div>
                ))}
              {!(hasDescription ? fields?.description : fields?.text) ? (
                <div
                  className={`text-base font-normal text-[#6b7280] mt-3 ${
                    hasSelect && hasDescription
                      ? manualPost?.data?.[card]?.uploadIds?.length
                        ? "line-clamp-2 h-[50px]"
                        : "line-clamp-8 h-[190px]"
                      : hasDescription
                      ? manualPost?.data?.[card]?.uploadIds?.length
                        ? "line-clamp-5 h-[120px]"
                        : "line-clamp-11 h-[265px]"
                      : ["REDDIT"].includes(card)
                      ? manualPost?.data?.[card]?.uploadIds?.length
                        ? "line-clamp-3 h-[75px]"
                        : "line-clamp-9 h-[210px]"
                      : manualPost?.data?.[card]?.uploadIds?.length
                      ? "line-clamp-6 h-[145px]"
                      : "line-clamp-12 h-[288px]"
                  }`}
                >
                  Start typing...
                </div>
              ) : (
                <pre
                  className={`text-base font-normal text-[#374151] mt-3 mb-0 text-wrap `}
                  style={{ fontFamily: "Inter" }}
                >
                  {hasDescription ? fields?.description : fields?.text}
                </pre>
              )}
            </div>
          ) : ( */}
          <Form
            name="dynamic_form"
            form={form}
            layout="vertical"
            onFinish={submitData}
            validateTrigger="onBlur"
          >
            {Object.keys(manualPost?.data?.[card] || {}).map((fieldKey) => {
              if (!["text", "description"].includes(fieldKey)) return null;

              const fieldRules = postValidationRules[card]?.[fieldKey] || {};
              const isRequired =
                postValidationRules[card]?.required?.includes(fieldKey);

              return (
                <Form.Item
                  key={fieldKey}
                  name={fieldKey}
                  className={`${
                    hasDescription
                      ? fieldKey === "description" && "mb-0"
                      : "mb-0"
                  }`}
                  rules={[
                    {
                      required: isRequired,
                      message: `Please enter ${fieldKey}!`,
                    },
                    {
                      whitespace: true,
                      message: "Input cannot be empty or spaces only",
                    },
                    ...(fieldRules.max
                      ? [
                          {
                            max: fieldRules.max,
                            message: `Max length is ${fieldRules.max} characters!`,
                          },
                        ]
                      : []),
                  ]}
                >
                  {fieldKey === "description" ||
                  (!hasDescription && fieldKey === "text") ? (
                    <Input.TextArea
                      placeholder={`Start typing...`}
                      onBlur={() => form.submit()}
                      style={{
                        backgroundColor: "transparent",
                      }}
                      readOnly={[
                        "PROCESSING",
                        "POSTED",
                        "ERROR",
                        "DELETED",
                      ].includes(postedInProgres)}
                      className="border border-gray-300 focus:bg-transparent focus:border-gray-300 hover:border-gray-300 focus:shadow-none overflow-hidden"
                      autoSize={{
                        minRows: hasDescription ? 3 : 6,
                      }}
                      maxLength={fieldRules.max || undefined}
                    />
                  ) : (
                    <Input
                      placeholder={`enter title`}
                      onBlur={() => form.submit()}
                      readOnly={[
                        "PROCESSING",
                        "POSTED",
                        "ERROR",
                        "DELETED",
                      ].includes(postedInProgres)}
                      maxLength={fieldRules.max || undefined}
                      style={{ backgroundColor: "transparent" }}
                      className="border border-gray-300 focus:bg-transparent focus:border-gray-300 hover:border-gray-300 focus:shadow-none"
                    />
                  )}
                </Form.Item>
              );
            })}
          </Form>
          {/* )} */}

          {!!manualPost?.data?.[card]?.uploadIds?.length && (
            <div className="flex gap-3 overflow-x-auto whitespace-nowrap pb-1 custom-scrollbar h-[122px]">
              {manualPost?.data?.[card]?.uploadIds?.map((mediaId, index) => (
                <Card
                  key={index}
                  styles={{
                    body: {
                      padding: 7,
                    },
                  }}
                  className="ant-card-custom w-28 h-28 flex-shrink-0 bg-[#ffffff87]"
                >
                  <div className="relative group w-24 h-24">
                    <img
                      src={fileList?.[mediaId]?.thumbnailUrl}
                      alt="Url"
                      className="object-cover w-24 h-24 rounded-md"
                    />
                    {fileList?.[mediaId]?.type !== "video" ? (
                      <Image
                        strokeWidth={1.5}
                        size={18}
                        color="#fff"
                        className="top-1 right-1 absolute bg-black/50 rounded-[4px] p-[2px]"
                      />
                    ) : (
                      <Play
                        strokeWidth={1.5}
                        color="#fff"
                        size={18}
                        className="top-1 right-1 absolute bg-black/50 rounded-[4px] p-[4px]"
                      />
                    )}
                    <div className="absolute w-24 h-24 top-0 flex items-center justify-center gap-2 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-md">
                      <Eye
                        strokeWidth={1.5}
                        size={20}
                        color="#fff"
                        className="cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          setOpen(fileList?.[mediaId]);
                        }}
                      />
                      {!["PROCESSING", "POSTED", "ERROR", "DELETED"].includes(
                        postedInProgres
                      ) && (
                        <Popconfirm
                          title="Delete Media"
                          description="Are you sure you want to delete this Media?"
                          icon={
                            <Info
                              strokeWidth={1.5}
                              size={16}
                              color="red"
                              className="mt-[2px] me-1"
                            />
                          }
                          okText="Yes"
                          cancelText="No"
                          onConfirm={(e) => {
                            e.stopPropagation();
                            onMediaDelete(mediaId);
                          }}
                          onCancel={(e) => e.stopPropagation()}
                        >
                          <Trash2
                            strokeWidth={1.5}
                            size={20}
                            color="#fff"
                            className="cursor-pointer"
                            onClick={(e) => e.stopPropagation()}
                          />
                        </Popconfirm>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}

          {hasSelect && (
            <>
              <Select
                placeholder={`Select ${card === "REDDIT" ? "sr" : "boardName"}`}
                showSearch
                onSearch={handleSearch}
                value={
                  manualPost?.data?.[card]?.[
                    card === "REDDIT" ? "sr" : "boardName"
                  ]
                }
                disabled={["PROCESSING", "POSTED", "ERROR", "DELETED"].includes(
                  postedInProgres
                )}
                onInputKeyDown={handleKeyDown}
                onChange={forSeleceOption}
                filterOption={(input, option) =>
                  option.label.toLowerCase().includes(input.toLowerCase())
                }
                options={boardName.map((item) => ({
                  label: item,
                  value: item,
                }))}
              />
            </>
          )}

          <Row justify="space-between" align="middle">
            <div className="text-sm font-normal text-[#6b7280]">
              {`${
                // isEdit
                content?.length || 0
                // : hasDescription
                // ? manualPost?.data?.[card]?.description?.length || 0
                // : manualPost?.data?.[card]?.text?.length || 0
              } / ${
                hasDescription
                  ? postValidationRules[card]?.description?.max
                  : postValidationRules[card]?.text?.max
              } characters`}
            </div>
            {!["PROCESSING", "POSTED", "ERROR", "DELETED"].includes(
              postedInProgres
            ) && (
              <Button
                onClick={() => setOpenMList(true)}
                type="default"
                className="remove-hover !hover:bg-transparent !hover:text-[#fb3748] shadow-none bg-transparent font-medium text-base"
              >
                Select Media
              </Button>
            )}
          </Row>
        </div>
      </Card>
      <Modal
        open={openMList}
        closeIcon={false}
        footer={null}
        centered
        onCancel={() => setOpenMList(false)}
        maskClosable={false}
        width={700}
        style={{ zoom: 0.9 }}
        className="rounded-[20px] md:rounded-[40px] overflow-hidden p-0 auth-modal"
        styles={{
          content: { padding: 0 },
          body: { padding: "50px" },
          mask: { backgroundColor: "rgba(0, 0, 0, 0.8)" },
        }}
      >
        <div className="flex items-center justify-between">
          <div className="sm:text-3xl text-2xl font-semibold text-[#020817]">
            Select Media
          </div>
          <IoCloseOutline
            size={32}
            color="#9ca8bc"
            className="cursor-pointer mt-[6px]"
            onClick={() => {
              setSelectedMedia(manualPost?.data?.[card]?.uploadIds);
              setOpenMList(false);
            }}
          />
        </div>
        <div className="text-sm font-normal text-[#6b7280] mb-10 pe-10">
          Select the media you want to add to your post.
        </div>
        <MediaList
          setOpenMList={setOpenMList}
          selectedMedia={selectedMedia}
          setSelectedMedia={setSelectedMedia}
          onSelect={onSelect}
          onCancel={() => {
            setSelectedMedia(manualPost?.data?.[card]?.uploadIds);
            setOpenMList(false);
          }}
          card={card}
        />
      </Modal>
      <MediaPreview open={open} setOpen={setOpen} />
    </>
  );
};

export default CustomCard;
