import { LoadingOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON>, Col, DatePicker, <PERSON>, Spin } from "antd";
import React, { useEffect } from "react";
import useHttp from "../../../hooks/use-http";
import StatusBadge from "./StatusBadge";
import moment from "moment";
import GradientButton from "../../../component/common/GradientButton";
import { CalendarClock, Send, Trash2 } from "lucide-react";
import CustomCard from "./CustomCard";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { apiGenerator, convertLocalToUTC } from "../../../util/functions";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { useWritePost } from "./WritePostContext";
import dayjs from "dayjs";
import postValidationRules from "../../../util/image";

const PostDetails = () => {
  const API = useHttp();
  const APIDRAFT = useHttp();
  const location = useLocation();
  const { setManualPost, manualPost, setRefersh, date, setDate } =
    useWritePost();
  const { postId, teamId } = useParams();
  const navigate = useNavigate();
  const postedInProgres =
    "SCHEDULED" === manualPost?.status &&
    manualPost?.postDate &&
    moment(manualPost.postDate).isBefore(moment())
      ? "PROCESSING"
      : manualPost?.status;

  useEffect(() => {
    let intervalId;

    if (postedInProgres === "PROCESSING") {
      intervalId = setInterval(() => {
        setRefersh((prev) => !prev);
      }, 7000);
    }

    return () => {
      clearInterval(intervalId);
    };
  }, [postedInProgres]);

  const validatePosts = (clickType) => {
    const valueObject = { ...manualPost?.data };
    let allValid = true;

    const checkValidation = Object.keys(valueObject).reduce((acc, platform) => {
      const rules = postValidationRules[platform]?.required;
      if (!rules) {
        acc[platform] = valueObject[platform];
        return acc;
      }

      let isValid = true;

      rules.forEach((rule) => {
        if (typeof rule === "string") {
          if (rule === "media") {
            if (!valueObject[platform]?.uploadIds?.length) {
              isValid = false;
            }
          } else if (!valueObject[platform]?.[rule]) {
            isValid = false;
          }
        } else if (rule.or) {
          const hasAtLeastOne = rule.or.some((key) => {
            if (key === "media") {
              return valueObject[platform]?.uploadIds?.length;
            }
            return valueObject[platform]?.[key];
          });

          if (!hasAtLeastOne) {
            isValid = false;
          }
        }
      });

      acc[platform] = {
        ...valueObject[platform],
        ...(isValid ? { validation: undefined } : { validation: false }), // Remove validation key if valid
      };

      if (isValid) {
        delete acc[platform].validation;
      } else allValid = false;

      return acc;
    }, {});

    if (!allValid) setManualPost((pre) => ({ ...pre, data: checkValidation }));

    if (!allValid) return;

    let payload = {
      data: checkValidation,
    };

    if (clickType === "Schedule") {
      payload = {
        ...payload,
        status: "SCHEDULED",
        postDate: date, // Use selected date
      };
    } else if (clickType === "PostNow") {
      payload = {
        ...payload,
        status: "SCHEDULED",
        postDate: convertLocalToUTC(moment()), // Convert current time to UTC
      };
    } else {
      payload = {
        ...payload,
        status: "DRAFT", // Default status if missing
        postDate: date || convertLocalToUTC(moment()), // Use existing or current UTC time
      };
    }

    updatePost(payload);
  };

  const onChange = (selectedD) => {
    // if (date) {
    //   const utcDate = convertLocalToUTC(date.$d);
    //   if (utcDate) {
    //     setManualPost((pre) => ({
    //       ...pre,
    //       postDate: utcDate,
    //     }));
    //   }
    // } else {
    //   setManualPost((pre) => {
    //     const { postDate, ...rest } = pre;
    //     return { ...rest };
    //   });
    // }
    if (selectedD) {
      const utcDate = convertLocalToUTC(selectedD.$d);
      if (utcDate) {
        setDate(utcDate);
      }
    } else {
      setDate(null);
    }
  };

  const updatePost = (payload) => {
    const apiInstance = payload?.status === "DRAFT" ? APIDRAFT : API;

    apiInstance.sendRequest(
      apiGenerator(CONSTANTS.API.post.updateOne, { postId: manualPost?.id }),
      (res) => {
        setRefersh((pre) => !pre);
      },
      payload
    );
  };

  const makeCopy = () => {
    API.sendRequest(
      apiGenerator(CONSTANTS.API.post.nakeCopy, { postId: manualPost?.id }),
      (res) => {
        if (res?.data?.socialBundlePostId)
          navigate(
            location.pathname.replace(/[^/]+$/, res?.data?.socialBundlePostId)
          );
      },
      {},
      "Post copied successfully."
    );
  };

  const onDelete = () => {
    API.sendRequest(
      apiGenerator(CONSTANTS.API.post.deletePost, { postId }),
      (res) => {
        navigate(`/team/${teamId}/posts`);
      },
      {},
      "Post Deleted successfully."
    );
  };

  return (
    <Spin
      spinning={API?.isLoading}
      indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
    >
      <Row className="w-full flex md:flex-row flex-col justify-between gap-4">
        <div className="text-xl font-semibold text-left">Posts</div>
        {!["PROCESSING", "POSTED", "ERROR", "DELETED"].includes(
          postedInProgres
        ) && (
          <div className="flex flex-wrap items-center gap-4">
            <DatePicker
              showTime={{ format: "HH:mm" }}
              format="YYYY-MM-DD HH:mm"
              placeholder="Schedule Post"
              value={date && dayjs(date)}
              disabledDate={(current) =>
                current && current < moment().startOf("day")
              }
              popupClassName="in-mobile"
              className="sm:max-w-52 w-full"
              onChange={onChange}
              suffixIcon={<CalendarClock size={22} color="#828997" />}
            />
            <div className="flex w-full md:w-auto gap-4">
              <GradientButton
                buttonText={
                  date && moment(date).isAfter(moment())
                    ? "Schedule"
                    : "Post Now"
                }
                className="w-1/2 md:w-auto px-6"
                onClick={() =>
                  validatePosts(
                    date && moment(date).isAfter(moment())
                      ? "Schedule"
                      : "PostNow"
                  )
                }
                loading={API.isLoading}
                icon={
                  date && moment(date).isAfter(moment()) ? (
                    ""
                  ) : (
                    <Send strokeWidth={1.5} color="#fff" />
                  )
                }
              />
              <Button
                type="default"
                onClick={() => validatePosts("Update")}
                className="w-1/2 md:w-auto px-6 h-[50px]"
                loading={APIDRAFT.isLoading}
              >
                Update
              </Button>
            </div>
          </div>
        )}
      </Row>
      <div className="columns-1 md:columns-2 xl:columns-3 gap-5 space-y-5">
        {Object.keys(manualPost?.data || {}).map((sm) => (
          <div key={sm} className="break-inside-avoid">
            <CustomCard card={sm} />
          </div>
        ))}
      </div>
      <Card className="p-2 md:rounded-[30px] rounded-[20px] mt-[30px] mb-5">
        <div className="flex justify-between items-center">
          <StatusBadge status={postedInProgres} />
          <div className="text-base font-normal text-[#6b7280]">
            {moment
              .utc(manualPost?.postDate)
              .local()
              .format("D MMMM YYYY, hh:mm A")}
          </div>
        </div>
        {manualPost?.postrequest?.prompt && (
          <pre
            className="font-normal text-lg text-[#020817] text-wrap mb-0"
            style={{ fontFamily: "Inter" }}
          >
            {manualPost?.postrequest?.prompt}
          </pre>
        )}
      </Card>
      {postedInProgres !== "PROCESSING" && (
        <Row className="w-full flex md:flex-row flex-col mt-[30px] mb-5 gap-4">
          <GradientButton
            buttonText="Make A Copy"
            // disabled={!manualPost?.postDate}
            className="min-w-52 sm:w-auto"
            onClick={makeCopy}
          />
          {manualPost?.status !== "DELETED" && (
            <Button
              type="default"
              onClick={onDelete}
              icon={
                <Trash2
                  size={23}
                  color="#6b7280"
                  strokeWidth={1.5}
                  className="cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                />
              }
              className="min-w-52 sm:w-auto h-[50px]"
            >
              Delete
            </Button>
          )}
        </Row>
      )}
    </Spin>
  );
};

export default PostDetails;
