import React, { useEffect, useRef, useState } from "react";
import InfiniteS<PERSON>roll from "react-infinite-scroll-component";
import { useParams } from "react-router-dom";
import useHttp from "../../../hooks/use-http";
import { apiGenerator } from "../../../util/functions";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { Col, Empty, Row, Select, Spin } from "antd";
import Post from "./Post";
import { LoadingOutlined } from "@ant-design/icons";
import { ChevronDown } from "lucide-react";

const Posts = () => {
  const { teamId } = useParams();
  const didMountRef = useRef(false);
  const API = useHttp();
  const API2 = useHttp();
  const [posts, setPosts] = useState([]);
  const [filter, setFilter] = useState(null);
  const [totalPost, setTotalPost] = useState(0);
  const [offset, setOffset] = useState(0);
  const [reFatch, setReFatch] = useState(false);

  const onDelete = (postId) => {
    if (postId) {
      API.sendRequest(
        apiGenerator(CONSTANTS.API.post.deletePost, { postId }),
        (res) => {
          setPosts((prevPosts) =>
            prevPosts.filter((post) => post?.socialBundlePostId !== postId)
          );
        },
        {},
        "Post Deleted successfully."
      );
    }
  };

  useEffect(() => {
    if (!teamId) return;

    const apiPoint = posts.length ? API2 : API;

    apiPoint.sendRequest(
      apiGenerator(
        CONSTANTS.API.post.getAll,
        {},
        `?teamId=${teamId}&limit=20&offset=${offset}${
          filter ? `&status=${filter}` : ""
        }`
      ),
      (res) => {
        setPosts((pre) => [...pre, ...res?.data?.posts]);
        setTotalPost(res?.data?.count);
      }
    );
  }, [offset, filter, reFatch]);

  useEffect(() => {
    if (!teamId) return;

    if (!didMountRef.current) {
      didMountRef.current = true;
      return;
    }

    setPosts([]);
    setOffset(0);
    setTotalPost(0);
    setReFatch((pre) => !pre);
  }, [teamId]);

  return (
    <div>
      <Row>
        <Select
          placeholder="Pick Status"
          value={filter}
          allowClear
          optionFilterProp="label"
          onChange={(val) => {
            setPosts([]);
            setOffset(0);
            setFilter(val);
          }}
          size="large"
          className="filter-status w-full sm:w-44 sm:mb-[30px] mb-5"
          options={[
            { value: "POSTED", label: "Posted" },
            { value: "ERROR", label: "Error" },
            { value: "SCHEDULED", label: "Scheduled" },
            { value: "PROCESSING", label: "Processing" },
            { value: "DELETED", label: "Deleted" },
            { value: "DRAFT", label: "Draft" },
          ]}
          suffixIcon={
            <ChevronDown
              size={22}
              strokeWidth={1.5}
              color="#828997"
              className="mt-2"
            />
          }
        />
      </Row>
      <Spin
        spinning={API?.isLoading}
        indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
      >
        {!API?.isLoading &&
          (posts?.length ? (
            <InfiniteScroll
              dataLength={posts?.length}
              next={() => setOffset((pre) => pre + 20)}
              hasMore={posts?.length !== totalPost}
              loader={<p className="text-center text-base">Loading...</p>}
            >
              <Row className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                {posts.map((post) => (
                  <Post
                    post={post}
                    key={post?.socialBundlePostId}
                    onDelete={() => onDelete(post?.socialBundlePostId)}
                  />
                ))}
              </Row>
            </InfiniteScroll>
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          ))}
      </Spin>
    </div>
  );
};

export default Posts;
