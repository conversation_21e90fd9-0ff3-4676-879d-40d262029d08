import React, { useEffect, useState } from "react";
import {
  <PERSON>tar,
  But<PERSON>,
  Card,
  Col,
  Form,
  Image,
  Input,
  Modal,
  notification,
  Popconfirm,
  Popover,
  Row,
} from "antd";
import GradientButton from "../../../component/common/GradientButton";
import { Eye, Info, Play, Trash2, Image as ImageIcon } from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import { apiKeyImage, socialMediaIcons } from "../../../util/image";
import MediaList from "../../../component/common/MediaList";
import { IoCloseOutline } from "react-icons/io5";
import { useWritePost } from "./WritePostContext";
import useHttp from "../../../hooks/use-http";
import { apiGenerator } from "../../../util/functions";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import MediaPreview from "../../../component/common/MediaPreview";
import Loader from "../../../component/common/Loader";

const Ai = () => {
  const API = useHttp();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { teamId } = useParams();
  const [openMList, setOpenMList] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState([]);
  const [selectedMedia, setSelectedMedia] = useState([]);
  const [finalMedia, setFinalMedia] = useState([]);
  const [open, setOpen] = useState(null);
  const { team, fileList, setAiPost, aiPost } = useWritePost();
  const [openSuccessModal, setOpenSuccessModal] = useState(false);
  const content = Form.useWatch("prompt", form);

  const handleSelect = (md) => {
    setSelectedPlatform((prev) =>
      prev.includes(md) ? prev.filter((item) => item !== md) : [...prev, md]
    );
  };

  useEffect(() => {
    form.resetFields();
    setSelectedPlatform([]);
  }, [teamId]);

  const createOne = (value) => {
    if (!selectedPlatform?.length) {
      notification.info({
        message: "Select Social Accounts:",
        description:
          "Please select one or more Social Accounts platforms to generate the post.",
      });
      return;
    }
    if (Object.keys(aiPost?.data || {})?.length)
      setAiPost({
        teamId: team?.id,
        status: "DRAFT",
        data: {},
      });
    const payload = {
      prompt: value?.prompt?.trim(),
      platforms: selectedPlatform,
      uploadIds: finalMedia,
    };
    API.sendRequest(
      apiGenerator(CONSTANTS.API.post.aiGeneratedPost, { teamId: team?.id }),
      (res) => {
        setAiPost((prev) => ({ ...prev, ...res?.data }));
      },
      payload,
      "",
      (errorHandler) => {
        setOpenSuccessModal(true);
      }
    );
  };
  return (
    <>
      {API?.isLoading && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50">
          <Loader />
        </div>
      )}
      <Row
        className={`flex flex-col gap-[30px] relative ${
          API?.isLoading && "blur-sm"
        }`}
      >
        <div className="flex items-center sm:gap-[30px] gap-4 flex-wrap">
          <div className="text-base font-normal text-[#020817]">
            Select Social Accounts:
          </div>
          <div className="flex flex-wrap items-center gap-4">
            {Object.keys(team)?.length &&
              (team?.socialAccounts?.length ? (
                team?.socialAccounts?.map((md) => (
                  <Avatar
                    key={md}
                    src={socialMediaIcons[md]}
                    size={44}
                    onClick={() => handleSelect(md)}
                    className={`border-[1px] p-2 bg-transparent cursor-pointer ${
                      selectedPlatform.includes(md)
                        ? "border-[#a855f7] bg-[#faf5ff]"
                        : "border-[#84868a]"
                    }`}
                  />
                ))
              ) : (
                <div className="text-base text-[#898c96]">
                  No social account connected
                </div>
              ))}
          </div>
        </div>
        <div className="flex items-center gap-[30px]">
          <div className="flex items-center gap-2">
            <div className="text-base font-normal text-[#020817]">
              Select Media
            </div>{" "}
            <Popover content={"This media will be used for the selected post."}>
              <Info strokeWidth={1.5} size={18} />
            </Popover>
          </div>
          <div>
            <Button
              onClick={() => setOpenMList(true)}
              className="font-semibold"
            >
              Select Media
            </Button>
          </div>
        </div>

        {finalMedia?.length ? (
          <div className="flex gap-3 whitespace-nowrap pb-1 custom-scrollbar h-[122px] overflow-x-auto w-full">
            {finalMedia?.map((mediaId, index) => (
              <Card
                key={index}
                styles={{
                  body: {
                    padding: 7,
                  },
                }}
                className="ant-card-custom w-28 h-28 flex-shrink-0 bg-[#ffffff87]"
              >
                <div className="relative group w-24 h-24">
                  <img
                    src={fileList?.[mediaId]?.thumbnailUrl}
                    alt="Url"
                    className="object-cover w-24 h-24 rounded-md"
                  />
                  {fileList?.[mediaId]?.type !== "video" ? (
                    <ImageIcon
                      strokeWidth={1.5}
                      size={18}
                      color="#fff"
                      className="top-1 right-1 absolute bg-black/50 rounded-[4px] p-[2px]"
                    />
                  ) : (
                    <Play
                      strokeWidth={1.5}
                      color="#fff"
                      size={18}
                      className="top-1 right-1 absolute bg-black/50 rounded-[4px] p-[4px]"
                    />
                  )}
                  <div className="absolute w-24 h-24 top-0 flex items-center justify-center gap-2 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-md">
                    <Eye
                      strokeWidth={1.5}
                      size={20}
                      color="#fff"
                      className="cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        setOpen(fileList?.[mediaId]);
                      }}
                    />
                    <Popconfirm
                      title="Delete media"
                      description="Are you sure you want to delete this media?"
                      icon={
                        <Info
                          strokeWidth={1.5}
                          size={16}
                          color="red"
                          className="mt-[2px] me-1"
                        />
                      }
                      okText="Yes"
                      cancelText="No"
                      onConfirm={(e) => {
                        e.stopPropagation();
                        const removeMedia = selectedMedia.filter(
                          (item) => item !== mediaId
                        );
                        setSelectedMedia(removeMedia);
                        setFinalMedia(removeMedia);
                      }}
                      onCancel={(e) => e.stopPropagation()}
                    >
                      <Trash2
                        strokeWidth={1.5}
                        size={20}
                        color="#fff"
                        className="cursor-pointer"
                        onClick={(e) => e.stopPropagation()}
                      />
                    </Popconfirm>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          ""
        )}

        <Form
          name="team_m"
          onFinish={createOne}
          className="w-full"
          validateTrigger="onBlur"
          form={form}
        >
          <Form.Item name={"prompt"}>
            <Input.TextArea
              className="w-full rounded-[20px] p-5"
              placeholder="Enter your content idea and select social account to auto generate posts"
              autoSize={{ minRows: 6, maxRows: 8 }}
            />
          </Form.Item>
          <Col span={8} xs={24} sm={12} md={12} lg={10} xl={7} xxl={6}>
            <Form.Item className="mb-0 mt-10">
              <GradientButton
                className="w-full"
                buttonText="Generate Content"
                htmlType="submit"
                disabled={!content?.trim()?.length}
                loading={API?.isLoading}
              />
            </Form.Item>
          </Col>
        </Form>
      </Row>
      <Modal
        open={openMList}
        closeIcon={false}
        footer={null}
        centered
        onCancel={() => setOpenMList(false)}
        maskClosable={false}
        width={700}
        style={{ zoom: 0.9 }}
        className="rounded-[20px] md:rounded-[40px] overflow-hidden p-0 auth-modal"
        styles={{
          content: { padding: 0 },
          body: { padding: "50px" },
          mask: { backgroundColor: "rgba(0, 0, 0, 0.8)" },
        }}
      >
        <div className="flex items-center justify-between">
          <div className="sm:text-3xl text-2xl font-semibold text-[#020817]">
            Select Media
          </div>
          <IoCloseOutline
            size={32}
            color="#9ca8bc"
            className="cursor-pointer mt-[6px]"
            onClick={() => {
              setSelectedMedia(finalMedia);
              setOpenMList(false);
            }}
          />
        </div>
        <div className="text-sm font-normal text-[#6b7280] mb-10 pe-10">
          Select the media you want to add to your post.
        </div>
        <MediaList
          setOpenMList={setOpenMList}
          selectedMedia={selectedMedia}
          setSelectedMedia={setSelectedMedia}
          onSelect={() => {
            setFinalMedia([...selectedMedia]);
            setOpenMList(false);
          }}
          onCancel={() => {
            setSelectedMedia(finalMedia);
            setOpenMList(false);
          }}
        />
      </Modal>
      <MediaPreview open={open} setOpen={setOpen} />.
      <Modal
        open={openSuccessModal}
        closeIcon={false}
        footer={null}
        centered
        onCancel={() => setOpenSuccessModal(false)}
        maskClosable={false}
        style={{ zoom: 0.9 }}
        width={500}
        className="rounded-[40px] overflow-hidden p-0 auth-modal"
        styles={{
          content: { padding: 0 },
          body: { padding: "50px" },
          mask: { backgroundColor: "rgba(0, 0, 0, 0.8)" },
        }}
      >
        <div className="mobilescale">
          <div className="flex flex-col mb-[50px]">
            <div className="flex justify-center">
              <Image src={apiKeyImage} width={90} preview={false} />
            </div>
            <div className="text-4xl font-semibold text-center mt-[30px]">
              Update Your API Key
            </div>
            <div className="text-lg text-[#6B7280] text-center mt-5">
              Incorrect API key provided, For continued access, please go to
              Settings and update your API key. You can find your API key at{" "}
              <a
                href="https://platform.openai.com/account/api-keys"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 underline hover:text-blue-800"
              >
                https://platform.openai.com/account/api-keys
              </a>
            </div>
          </div>
          <div className="flex gap-4">
            <GradientButton
              htmlType="submit"
              className="w-1/2 "
              buttonText="Go To Settings"
              onClick={() => navigate(`/team/${team?.id}/settings/company`)}
            />
            <Button
              className="w-1/2"
              onClick={() => setOpenSuccessModal(false)}
            >
              Cancel
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default Ai;
