import { Avatar, <PERSON><PERSON>, Card, Col, <PERSON><PERSON><PERSON>, <PERSON>, Tabs } from "antd";
import TabPane from "antd/es/tabs/TabPane";
import React, { useState } from "react";
import Ai from "./Ai";
import moment from "moment";
import postValidationRules, {
  manualPostJason,
  socialMediaIcons,
} from "../../../util/image";
import CustomCard from "./CustomCard";
import { useWritePost } from "./WritePostContext";
import GradientButton from "../../../component/common/GradientButton";
import { CalendarClock, Send } from "lucide-react";
import { apiGenerator, convertLocalToUTC } from "../../../util/functions";
import useHttp from "../../../hooks/use-http";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { useNavigate, useParams } from "react-router-dom";
import CustomAiCard from "./CustomAiCard";

const Dashboard = () => {
  const API = useHttp();
  const APIDRAFT = useHttp();
  const { teamId } = useParams();
  const navigate = useNavigate();
  const { team, manualPost, setManualPost, aiPost, setAiPost } = useWritePost();
  const [tab, setTab] = useState("2");

  const handleSelect = (md) => {
    if (manualPost?.data?.[md]) return;
    setManualPost((pre) => {
      //   {
      //   const updatedData = { ...pre?.data };
      //   delete updatedData[md];

      //   return { ...pre, data: updatedData };
      // }
      return {
        ...pre,
        data: { ...pre?.data, [md]: manualPostJason[md] },
      };
    });
  };

  const validatePosts = (clickType) => {
    const valueObject = { ...manualPost?.data };
    let allValid = true;

    const checkValidation = Object.keys(valueObject).reduce((acc, platform) => {
      const rules = postValidationRules[platform]?.required;
      if (!rules) {
        acc[platform] = valueObject[platform];
        return acc;
      }

      let isValid = true;

      rules.forEach((rule) => {
        if (typeof rule === "string") {
          if (rule === "media") {
            if (!valueObject[platform]?.uploadIds?.length) {
              isValid = false;
            }
          } else if (!valueObject[platform]?.[rule]) {
            isValid = false;
          }
        } else if (rule.or) {
          const hasAtLeastOne = rule.or.some((key) => {
            if (key === "media") {
              return valueObject[platform]?.uploadIds?.length;
            }
            return valueObject[platform]?.[key];
          });

          if (!hasAtLeastOne) {
            isValid = false;
          }
        }
      });

      acc[platform] = {
        ...valueObject[platform],
        ...(isValid ? { validation: undefined } : { validation: false }), // Remove validation key if valid
      };

      if (isValid) {
        delete acc[platform]?.validation;
      } else allValid = false;

      return acc;
    }, {});

    if (!allValid) setManualPost((pre) => ({ ...pre, data: checkValidation }));

    if (!allValid) return;

    let payload = {
      ...manualPost,
      data: checkValidation,
    };
    if (clickType === "DRAFT") {
      payload = {
        ...payload,
        status: "DRAFT",
      };
    } else if (clickType === "Post") {
      payload = {
        ...payload,
        postDate: convertLocalToUTC(moment()),
        status: "SCHEDULED",
      };
    }

    createPost(payload);
  };

  const validateAiPosts = (clickType) => {
    const valueObject = { ...aiPost?.data };
    let allValid = true;

    const checkValidation = Object.keys(valueObject).reduce((acc, platform) => {
      const rules = postValidationRules[platform]?.required;
      if (!rules) {
        acc[platform] = valueObject[platform];
        return acc;
      }

      let isValid = true;

      rules.forEach((rule) => {
        if (typeof rule === "string") {
          if (rule === "media") {
            if (!valueObject[platform]?.uploadIds?.length) {
              isValid = false;
            }
          } else if (!valueObject[platform]?.[rule]) {
            isValid = false;
          }
        } else if (rule.or) {
          const hasAtLeastOne = rule.or.some((key) => {
            if (key === "media") {
              return valueObject[platform]?.uploadIds?.length;
            }
            return valueObject[platform]?.[key];
          });

          if (!hasAtLeastOne) {
            isValid = false;
          }
        }
      });

      acc[platform] = {
        ...valueObject[platform],
        ...(isValid ? { validation: undefined } : { validation: false }), // Remove validation key if valid
      };

      if (isValid) {
        delete acc[platform]?.validation;
      } else allValid = false;

      return acc;
    }, {});

    if (!allValid) setAiPost((pre) => ({ ...pre, data: checkValidation }));

    if (!allValid) return;

    let payload = {
      ...aiPost,
      data: checkValidation,
    };
    delete payload.postRequestId;
    if (clickType === "DRAFT") {
      payload = {
        ...payload,
        status: "DRAFT",
      };
    } else if (clickType === "Post") {
      payload = {
        ...payload,
        postDate: convertLocalToUTC(moment()),
        status: "SCHEDULED",
      };
    }

    createAiPost(payload, aiPost?.postRequestId);
  };

  const onChange = (date) => {
    if (date) {
      const utcDate = convertLocalToUTC(date?.$d);
      if (utcDate) {
        setManualPost((pre) => ({
          ...pre,
          postDate: utcDate,
          status: "SCHEDULED",
        }));
      }
    } else {
      setManualPost((pre) => {
        const { postDate, status, ...rest } = pre;
        return { ...rest, status: "DRAFT" };
      });
    }
  };

  const onChangeAiPostDate = (date) => {
    if (date) {
      const utcDate = convertLocalToUTC(date?.$d);
      if (utcDate) {
        setAiPost((pre) => ({
          ...pre,
          postDate: utcDate,
          status: "SCHEDULED",
        }));
      }
    } else {
      setAiPost((pre) => {
        const { postDate, status, ...rest } = pre;
        return { ...rest, status: "DRAFT" };
      });
    }
  };

  const createPost = (payload) => {
    const apiInstance = payload.status === "DRAFT" ? APIDRAFT : API;

    apiInstance.sendRequest(
      CONSTANTS.API.post.create,
      (res) => {
        if (res?.data?.socialBundlePostId && teamId) {
          navigate(`/team/${teamId}/posts/${res.data.socialBundlePostId}`);
        }
      },
      payload
    );
  };

  const createAiPost = (payload, postRequestId) => {
    if (!postRequestId) return;
    const apiInstance = payload.status === "DRAFT" ? APIDRAFT : API;

    apiInstance.sendRequest(
      apiGenerator(
        CONSTANTS.API.post.create,
        {},
        `?isAiGenerated=${true}&postrequestId=${postRequestId}`
      ),
      (res) => {
        if (res?.data?.socialBundlePostId && teamId) {
          navigate(`/team/${teamId}/posts/${res.data.socialBundlePostId}`);
        }
      },
      payload
    );
  };

  return (
    <>
      <Card
        className="w-full rounded-[30px]"
        styles={{
          body: { padding: 30 },
          header: { justifyContent: "end", height: 70 },
        }}
        title={
          <>
            <Tabs
              activeKey={tab}
              onChange={(key) => setTab(key)}
              className="write-post"
            >
              <TabPane
                key="2"
                tab={<span className="text-lg font-normal">Manual Post</span>}
              />
              <TabPane
                key="1"
                tab={
                  <span className="text-lg font-normal mb-5">
                    AI Generated Post
                  </span>
                }
              />
            </Tabs>
          </>
        }
      >
        {tab === "1" && <Ai />}
        {tab === "2" && (
          <Row className="flex flex-col gap-[30px]">
            <div className="flex items-center gap-[30px] flex-wrap">
              <div className="text-base font-normal text-[#020817]">
                Select Social Accounts:
              </div>
              <div className="flex flex-wrap items-center gap-4">
                {Object.keys(team)?.length &&
                  (team?.socialAccounts?.length ? (
                    team?.socialAccounts?.map((md) => (
                      <Avatar
                        key={md}
                        src={socialMediaIcons[md]}
                        size={44}
                        onClick={() => handleSelect(md)}
                        className={`border-[1px] p-2 bg-transparent cursor-pointer ${
                          manualPost?.data?.[md]
                            ? "border-[#a855f7] bg-[#faf5ff]"
                            : "border-[#d9dbdf]"
                        }`}
                      />
                    ))
                  ) : (
                    <div className="text-base text-[#898c96]">
                      No social account connected
                    </div>
                  ))}
              </div>
            </div>
          </Row>
        )}
      </Card>
      {tab === "2" && Object.keys(manualPost?.data)?.length ? (
        <>
          <Row className="w-full flex md:flex-row flex-col justify-between mt-[30px] mb-5 gap-4">
            <div className="text-xl font-semibold text-left">Post Preview</div>

            <div className="flex flex-wrap items-center gap-4">
              <DatePicker
                showTime={{ format: "HH:mm" }}
                format="YYYY-MM-DD HH:mm"
                placeholder="Schedule Post"
                disabledDate={(current) =>
                  current && current < moment().startOf("day")
                }
                popupClassName="in-mobile"
                className="sm:max-w-52 w-full"
                onChange={onChange}
                suffixIcon={<CalendarClock size={22} color="#828997" />}
              />
              <div className="flex w-full md:w-auto gap-4">
                <GradientButton
                  buttonText={
                    manualPost?.postDate &&
                    moment(manualPost?.postDate).isAfter(moment())
                      ? "Schedule"
                      : "Post Now"
                  }
                  className="w-52 md:w-auto px-6"
                  onClick={() =>
                    validatePosts(
                      manualPost?.postDate &&
                        moment(manualPost?.postDate).isAfter(moment())
                        ? "Schedule"
                        : "Post"
                    )
                  }
                  loading={API.isLoading}
                  icon={
                    manualPost?.postDate &&
                    moment(manualPost?.postDate).isAfter(moment()) ? (
                      ""
                    ) : (
                      <Send strokeWidth={1.5} color="#fff" />
                    )
                  }
                />
                <Button
                  type="default"
                  onClick={() => validatePosts("DRAFT")}
                  className="w-52 md:w-auto px-6"
                  loading={APIDRAFT.isLoading}
                >
                  Save As Draft
                </Button>
              </div>
            </div>
          </Row>

          {/* <Row gutter={[24, 24]}>
            {Object.keys(manualPost?.data || {}).map((sm) => (
              <Col span={24} md={12} xxl={8}>
                <CustomCard card={sm} />
              </Col>
            ))}
          </Row> */}
          <div className="columns-1 md:columns-2 xl:columns-3 gap-5 space-y-5">
            {Object.keys(manualPost?.data || {}).map((sm) => (
              <div key={sm} className="break-inside-avoid">
                <CustomCard card={sm} />
              </div>
            ))}
          </div>
        </>
      ) : tab === "1" && Object.keys(aiPost?.data)?.length ? (
        <>
          <Row className="w-full flex md:flex-row flex-col justify-between mt-[30px] mb-5 gap-4">
            <div className="text-xl font-semibold text-left">Post Preview</div>

            <div className="flex flex-wrap items-center gap-4">
              <DatePicker
                showTime={{ format: "HH:mm" }}
                format="YYYY-MM-DD HH:mm"
                placeholder="Schedule Post"
                disabledDate={(current) =>
                  current && current < moment().startOf("day")
                }
                popupClassName="in-mobile"
                className="sm:max-w-52 w-full"
                onChange={onChangeAiPostDate}
                suffixIcon={<CalendarClock size={22} color="#828997" />}
              />
              <div className="flex w-full md:w-auto gap-4">
                <GradientButton
                  buttonText={
                    aiPost?.postDate &&
                    moment(aiPost?.postDate).isAfter(moment())
                      ? "Schedule"
                      : "Post Now"
                  }
                  className="w-52 md:w-auto px-6"
                  onClick={() =>
                    validateAiPosts(
                      aiPost?.postDate &&
                        moment(aiPost?.postDate).isAfter(moment())
                        ? "Schedule"
                        : "Post"
                    )
                  }
                  loading={API.isLoading}
                  icon={
                    aiPost?.postDate &&
                    moment(aiPost?.postDate).isAfter(moment()) ? (
                      ""
                    ) : (
                      <Send strokeWidth={1.5} color="#fff" />
                    )
                  }
                />
                <Button
                  type="default"
                  onClick={() => validateAiPosts("DRAFT")}
                  className="w-52 md:w-auto px-6"
                  loading={APIDRAFT.isLoading}
                >
                  Save As Draft
                </Button>
              </div>
            </div>
          </Row>

          {/* <Row gutter={[24, 24]}>
            {Object.keys(aiPost?.data || {}).map((sm) => (
              <Col span={24} md={12} xxl={8}>
                <CustomAiCard card={sm} />
              </Col>
            ))}
          </Row> */}
          <div className="columns-1 md:columns-2 xl:columns-3 gap-5 space-y-5">
            {Object.keys(aiPost?.data || {}).map((sm) => (
              <div key={sm} className="break-inside-avoid">
                <CustomAiCard card={sm} />
              </div>
            ))}
          </div>
        </>
      ) : (
        <></>
      )}
    </>
  );
};

export default Dashboard;
