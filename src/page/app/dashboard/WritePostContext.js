import { createContext, useContext, useEffect, useState } from "react";
import useHttp from "../../../hooks/use-http";
import { useParams } from "react-router-dom";
import { apiGenerator } from "../../../util/functions";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";

const WritePostContext = createContext();

export const WritePostProvider = ({ children }) => {
  const API = useHttp();
  const { teamId } = useParams();
  const [team, setTeam] = useState({});
  const [fileList, setFileList] = useState({});
  const [manualPost, setManualPost] = useState({
    teamId: +teamId,
    status: "DRAFT",
    data: {},
  });

  const [aiPost, setAiPost] = useState({
    teamId: +teamId,
    status: "DRAFT",
    data: {},
  });

  const getUpdatedData = () => {
    if (!teamId) return;
    API.sendBulkRequest(
      [
        apiGenerator(CONSTANTS.API.teams.getOne, { teamId }),
        apiGenerator(CONSTANTS.API.media.getAll, {}, `?teamId=${teamId}`),
      ],
      (res) => {
        setManualPost({
          teamId: +teamId,
          status: "DRAFT",
          data: {},
        });
        setAiPost({
          teamId: +teamId,
          status: "DRAFT",
          data: {},
        });
        setTeam(res?.[0]?.data?.data);
        setFileList(
          res?.[1]?.data?.data?.data?.reduce((acc, item) => {
            if (item?.id) {
              acc[item.id] = item;
            }
            return acc;
          }, {})
        );
      }
    );
  };

  useEffect(() => {
    getUpdatedData();
  }, [teamId]);

  return (
    <WritePostContext.Provider
      value={{
        team,
        manualPost,
        setManualPost,
        fileList,
        setFileList,
        aiPost,
        setAiPost,
      }}
    >
      <Spin
        spinning={API?.isLoading}
        indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
      >
        {children}
      </Spin>
    </WritePostContext.Provider>
  );
};

export const useWritePost = () => useContext(WritePostContext);
