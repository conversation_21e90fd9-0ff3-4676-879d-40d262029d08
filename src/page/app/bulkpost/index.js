import React, { useCallback, useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Alert,
  Avatar,
  Button,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Image,
  Input,
  Layout,
  notification,
  Popconfirm,
  Popover,
  Radio,
  Row,
  Steps,
  Upload,
} from "antd";
import useHttp from "../../../hooks/use-http";
import { getAuthToken } from "../../../util/API/authStorage";
import logo from "../../../asset/logos/icon.svg";
import CONSTANTS, {
  appRoot,
  loginRoot,
} from "../../../util/constant/CONSTANTS";
import RightDrawer from "../../../component/layout/Component/right-drawer";
import postValidationRules, {
  manualPostJason,
  postRequiredSchema,
  socialMediaIcons,
} from "../../../util/image";
import { InfoCircleOutlined } from "@ant-design/icons";
import {
  Calendar<PERSON>lock,
  Eye,
  ImageIcon,
  Info,
  Play,
  Plus,
  Search,
  Trash2,
} from "lucide-react";
import TextArea from "antd/es/input/TextArea";
import GradientButton from "../../../component/common/GradientButton";
import { apiGenerator, convertLocalToUTC } from "../../../util/functions";
import moment from "moment";
import MediaPreview from "../../../component/common/MediaPreview";

const { Header, Content } = Layout;

const contentOptions = [
  { value: "same-content", label: "Same Content" },
  { value: "different-content", label: "Different Content" },
];

const mediaOptions = [
  { value: "no-media", label: "No Media" },
  { value: "randomize-media", label: "Randomize Media" },
  { value: "upload-media", label: "Upload Media" },
];

const uploadMediaOptions = [
  { value: "same-media", label: "Same Media" },
  { value: "different-media", label: "Different Media" },
];

const BulkPost = (props) => {
  const API = useHttp();
  const [form] = Form.useForm();
  const { user_details } = props;
  const subscriptionPlan = props?.user_details?.plan;
  const navigate = useNavigate();
  const [visible, setVisible] = useState(false);
  const [step, setStep] = useState(0);
  // step 1
  const [selectedPlatform, setSelectedPlatform] = useState({});
  // setp 2
  const [teams, setTeams] = useState({});
  const [selectedTeams, setSelectedTeams] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  const [contentConfig, setContentConfig] = useState(null);
  const [mediaConfig, setMediaConfig] = useState(null);
  const [uploadMediaConfig, setUploadMediaConfig] = useState(null);
  const [postDate, setPostDate] = useState(null);
  const debounceRef = useRef(null);
  //sept 3
  const [postJson, setPostJson] = useState({});
  const [currentPlatform, setCurrentPlatform] = useState(null);
  const [currentTeam, setCurrentTeam] = useState(null);
  //file upload
  const [fileListState, setFileList] = useState({});
  const [openPreview, setOpenPreview] = useState(null);

  const getAllTeam = async (search = "") => {
    const limit = 1000;
    let page = 1;
    let allTeams = {};

    while (true) {
      const queryParams = `?limit=${limit}&page=${page}${
        search ? `&search=${encodeURIComponent(search)}` : ""
      }`;

      const res = await new Promise((resolve, reject) => {
        API.sendRequest(
          apiGenerator(CONSTANTS.API.teams.getAll, {}, queryParams),
          resolve,
          reject
        );
      });

      const data = res?.data;
      const response = data?.response || [];

      // Add to allTeams
      response.forEach((item) => {
        if (item?.id) {
          allTeams[item.id] = item;
        }
      });

      // Break if all data fetched
      if (page * limit >= data?.count) break;
      page++;
    }

    setTeams(allTeams);
  };

  console.log(fileListState, postJson);

  useEffect(() => {
    const isLogin = getAuthToken() !== undefined && getAuthToken() !== null;
    if (!isLogin) {
      navigate(loginRoot);
      return;
    }
  }, [user_details, navigate]);

  useEffect(() => {
    if (debounceRef.current) clearTimeout(debounceRef.current);

    debounceRef.current = setTimeout(() => {
      getAllTeam(searchValue);
    }, 300);

    return () => clearTimeout(debounceRef.current);
  }, [searchValue]);

  const handleUploadChange = async ({ file }) => {
    const id = `${file.uid}-${Date.now()}`;
    const preview = URL.createObjectURL(file);
    const newFile = {
      id,
      file,
      preview,
      progress: 0,
      uploading: true,
      name: `${currentPlatform}-${file.uid}-${Date.now()}`, // here we need to pass uniqname every time
    };

    console.log(newFile.id);

    if (uploadMediaConfig === "same-media")
      setFileList((prev) => ({
        ...prev,
        [currentPlatform]: [...(prev[currentPlatform] || []), newFile],
      }));
    else
      setFileList((prev) => ({
        ...prev,
        [currentTeam]: {
          ...prev[currentTeam],
          [currentPlatform]: [
            ...(prev[currentTeam]?.[currentPlatform] || []),
            newFile,
          ],
        },
      }));

    // Simulate upload progress
    const simulateProgress = () => {
      let percent = 0;
      const interval = setInterval(() => {
        percent += 10;
        if (uploadMediaConfig === "same-media") {
          setFileList((prev) => ({
            ...prev,
            [currentPlatform]: prev[currentPlatform].map((f) =>
              f.id === id ? { ...f, progress: percent } : f
            ),
          }));
        } else {
          setFileList((prev) => ({
            ...prev,
            [currentTeam]: {
              ...prev[currentTeam],
              [currentPlatform]: prev[currentTeam]?.[currentPlatform].map((f) =>
                f.id === id ? { ...f, progress: percent } : f
              ),
            },
          }));
        }
        if (percent >= 100) {
          clearInterval(interval);
          if (uploadMediaConfig === "same-media") {
            setFileList((prev) => ({
              ...prev,
              [currentPlatform]: prev[currentPlatform].map((f) =>
                f.id === id ? { ...f, uploading: false } : f
              ),
            }));
          } else {
            setFileList((prev) => ({
              ...prev,
              [currentTeam]: {
                ...prev[currentTeam],
                [currentPlatform]: prev[currentTeam]?.[currentPlatform].map(
                  (f) => (f.id === id ? { ...f, uploading: false } : f)
                ),
              },
            }));
          }
        }
      }, 100);
    };
    simulateProgress();
  };

  const beforeUpload = async (file, fileList) => {
    const imageTypes = ["image/jpeg", "image/jpg", "image/png"];
    const videoTypes = ["video/mp4"];
    const isImage = imageTypes.includes(file.type);
    const isVideo = videoTypes.includes(file.type);

    const rules = postValidationRules[currentPlatform]?.media;
    if (!rules) return Upload.LIST_IGNORE;

    if (!isImage && !isVideo) {
      notification.info({
        message: "Only JPG, JPEG, PNG images and MP4 videos are allowed.",
        duration: 3,
      });
      return false;
    }

    // Size validation
    if (isImage && file.size / 1024 / 1024 > 25) {
      notification.warning({
        message: "Image file size must be less than 25MB.",
      });
      return false;
    }
    if (isVideo && file.size / 1024 / 1024 > 1024) {
      notification.warning({
        message: "Video file size must be less than 1GB.",
      });
      return false;
    }

    // Current media already uploaded
    const mediaList =
      uploadMediaConfig === "same-media"
        ? fileListState[currentPlatform] || []
        : fileListState[currentTeam]?.[currentPlatform] || [];

    const currentImages = mediaList.filter((f) =>
      imageTypes.includes(f?.file?.type)
    );
    const currentVideos = mediaList.filter((f) =>
      videoTypes.includes(f?.file?.type)
    );

    const newImageCount = fileList.filter((f) =>
      imageTypes.includes(f?.type)
    ).length;
    const newVideoCount = fileList.filter((f) =>
      videoTypes.includes(f?.type)
    ).length;

    if (typeof rules.allowed === "number") {
      const totalCount = mediaList.length + fileList.length;
      if (totalCount > rules.allowed) {
        notification.warning({
          message: "Media Upload Limit Reached",
          description: rules.mediaValidationMessage,
        });
        return Upload.LIST_IGNORE;
      }
    } else if (typeof rules.allowed === "object") {
      if (isVideo) {
        if (
          currentImages.length > 0 ||
          currentVideos.length + newVideoCount > (rules.allowed.video || 0)
        ) {
          notification.warning({
            message: "Video Upload Not Allowed",
            description: rules.mediaValidationMessage,
          });
          return Upload.LIST_IGNORE;
        }
      }

      if (isImage) {
        if (
          currentVideos.length > 0 ||
          currentImages.length + newImageCount > (rules.allowed.image || 0)
        ) {
          notification.warning({
            message: "Image Upload Not Allowed",
            description: rules.mediaValidationMessage,
          });
          return Upload.LIST_IGNORE;
        }
      }
    }

    const checkAspectRatio = () => {
      const ratioRules = rules?.ratio?.[isImage ? "image" : "video"];

      const isEmptyRule = (rule) =>
        (!rule?.minRatio && !rule?.maxRatio) ||
        (rule?.minRatio === null && rule?.maxRatio === null);

      // ✅ Early skip if no ratio rules or all null
      if (
        !ratioRules ||
        (Array.isArray(ratioRules) && ratioRules.every(isEmptyRule)) ||
        (!Array.isArray(ratioRules) && isEmptyRule(ratioRules))
      ) {
        return Promise.resolve(true); // ✅ Skip validation
      }

      // ✅ Proceed to load file only if ratio needs to be validated
      return new Promise((resolve) => {
        const url = URL.createObjectURL(file);
        const media = isImage ? new Image() : document.createElement("video");

        const toFloat = (ratioStr) => {
          if (!ratioStr || ratioStr === "0") return null;
          const [w, h] = ratioStr.split(":").map(Number);
          return h !== 0 ? w / h : null;
        };

        const validateRatio = (rule, actualRatio) => {
          const min = toFloat(rule.minRatio);
          const max = toFloat(rule.maxRatio);

          if (min === null && max === null) return true;
          if (min === null) return actualRatio <= max;
          if (max === null) return actualRatio >= min;

          return actualRatio >= min && actualRatio <= max;
        };

        media.onload = () => {
          const width = media.videoWidth || media.width;
          const height = media.videoHeight || media.height;
          const actualRatio = width / height;

          const isValid = Array.isArray(ratioRules)
            ? ratioRules.some((r) => validateRatio(r, actualRatio))
            : validateRatio(ratioRules, actualRatio);

          if (!isValid) {
            const formattedRatios = Array.isArray(ratioRules)
              ? ratioRules
                  .filter((r) => !isEmptyRule(r))
                  .map((r) => `${r.minRatio || ""} ~ ${r.maxRatio || ""}`)
                  .join(" OR ")
              : `${ratioRules.minRatio || ""} ~ ${ratioRules.maxRatio || ""}`;

            notification.warning({
              message: "Invalid Aspect Ratio",
              description: `Allowed aspect ratio(s): ${formattedRatios}`,
            });

            resolve(false);
          } else {
            resolve(true);
          }

          URL.revokeObjectURL(url);
        };

        if (!isImage) {
          media.preload = "metadata";
        }

        media.src = url;
      });
    };

    const isValidRatio = await checkAspectRatio();
    if (!isValidRatio) return isValidRatio;

    return true;
  };

  const handleDelete = (id) => {
    if (uploadMediaConfig === "same-media") {
      setFileList((prev) => ({
        ...prev,
        [currentPlatform]: prev[currentPlatform].filter((f) => f.id !== id),
      }));
    } else {
      setFileList((prev) => ({
        ...prev,
        [currentTeam]: {
          ...prev[currentTeam],
          [currentPlatform]: prev[currentTeam]?.[currentPlatform].filter(
            (f) => f.id !== id
          ),
        },
      }));
    }
  };

  const onNext = () => {
    if (step === 1) {
      if (contentConfig === "same-content") {
        setPostJson(selectedPlatform);
      } else if (contentConfig === "different-content") {
        setPostJson((prev) => {
          return selectedTeams.reduce((acc, teamid) => {
            acc[teamid] = prev[teamid] || selectedPlatform;
            return acc;
          }, {});
        });
      }
      setCurrentPlatform(Object.keys(selectedPlatform)?.[0]);
      setCurrentTeam(selectedTeams?.[0]);
    }
    setStep((pre) => pre + 1);
  };

  const includesPlatform = (platforms, keysToCheck) =>
    keysToCheck.some((key) => platforms?.hasOwnProperty(key));

  const onSubmit = async () => {
    const isSelectedPinterestOrReddit = includesPlatform(selectedPlatform, [
      "PINTEREST",
      "REDDIT",
    ]);

    const clonedSelectedTeams = [...selectedTeams];

    const getJsonData = (teamId) => {
      const base =
        contentConfig === "same-content" ? postJson : postJson[teamId];
      return JSON.parse(JSON.stringify(base));
    };

    const enrichWithChannelData = async (teamId, jsonData) => {
      try {
        await API.sendRequest(
          apiGenerator(CONSTANTS.API.teams.getChannel, { teamId }),
          (res) => {
            const rows = res?.data?.rows || [];

            rows.forEach((row) => {
              const platform = row.platform?.toUpperCase();
              if (platform === "REDDIT" && jsonData["REDDIT"]) {
                jsonData["REDDIT"].sr = row.channel;
              }
              if (platform === "PINTEREST" && jsonData["PINTEREST"]) {
                jsonData["PINTEREST"].boardName = row.channel;
              }
            });
          }
        );
      } catch (err) {
        console.error("Error fetching channels for team", teamId, err);
      }
    };

    const teamPost = async (payload, teamId) => {
      try {
        await API.sendRequest(
          CONSTANTS.API.bulkPost.noMedinAndRandomMediaPost,
          (res) => {
            if (contentConfig === "different-content") {
              delete postJson[teamId];
              setPostDate((prev) => ({ ...prev }));
            }
          },
          payload
        );
      } catch (error) {
        console.error(`Error posting to team ${teamId}:`, error);
      }
    };

    const handleMediaUpload = async (teamId, jsonData) => {
      const formData = new FormData();
      formData.append("teamIds", teamId);

      if (uploadMediaConfig === "same-media") {
        Object.keys(fileListState)?.forEach((platform) => {
          const mediaFiles = fileListState[platform] || [];
          mediaFiles.forEach((item) => {
            if (!item?.isUploaded)
              formData.append("file", item.file, item.name);
          });
        });
      } else {
        const teamMedia = fileListState[teamId] || {};
        Object.keys(teamMedia)?.forEach((platform) => {
          const mediaFiles = teamMedia[platform] || [];
          mediaFiles.forEach((item) => {
            if (!item?.isUploaded)
              formData.append("file", item.file, item.name);
          });
        });
      }

      try {
        await API.sendRequest(
          CONSTANTS.API.media.uploadMultiple,
          (res) => {
            if (res?.mediaData?.length) {
              res.mediaData.forEach((media) => {
                const [platform] = media.name.split("-");

                // --- 1. Add to jsonData[platform].uploadIds ---
                if (!jsonData[platform]) jsonData[platform] = {};
                if (!Array.isArray(jsonData[platform].uploadIds)) {
                  jsonData[platform].uploadIds = [];
                }

                jsonData[platform].uploadIds.push(media.id);

                // --- 2. Mark as uploaded in fileList with setFileList ---
                if (uploadMediaConfig === "same-media") {
                  setFileList((prev) => {
                    const updated = { ...prev };
                    const platformFiles = [...(updated[platform] || [])];
                    const updatedFiles = platformFiles.map((item) =>
                      item.name === media.name
                        ? { ...item, isUploaded: true, uploadIds: media.id }
                        : item
                    );
                    updated[platform] = updatedFiles;
                    return updated;
                  });
                } else {
                  setFileList((prev) => {
                    const updated = { ...prev };
                    const teamMedia = { ...(updated[teamId] || {}) };
                    const platformFiles = [...(teamMedia[platform] || [])];
                    const updatedFiles = platformFiles.map((item) =>
                      item.name === media.name
                        ? { ...item, isUploaded: true, uploadIds: media.id }
                        : item
                    );
                    teamMedia[platform] = updatedFiles;
                    updated[teamId] = teamMedia;
                    return updated;
                  });
                }
              });
            }
          },
          formData,
          null
        );
      } catch (error) {
        console.error("Error uploading media for team", teamId, error);
      }

      return jsonData;
    };

    // --- MAIN LOOP ---
    for (const teamId of clonedSelectedTeams) {
      const jsonData = getJsonData(teamId);

      if (isSelectedPinterestOrReddit) {
        await enrichWithChannelData(teamId, jsonData);
      }

      if (mediaConfig === "upload-media") {
        await handleMediaUpload(teamId, jsonData);
      }

      const payload = {
        teamId,
        randomMedia: mediaConfig === "random-media",
        postDate,
        data: jsonData,
      };

      await teamPost(payload, teamId);
    }

    // Uncomment if redirection needed
    // navigate(`${appRoot}/bulkpost/success`);
  };

  const onChange = (date) => {
    if (date) {
      const utcDate = convertLocalToUTC(date?.$d);
      setPostDate(utcDate);
    } else {
      setPostDate(null);
    }
  };

  // console.log(postJson);

  const getCurrentFormData = useCallback(() => {
    if (contentConfig === "same-content") {
      return postJson[currentPlatform] || {};
    } else if (contentConfig === "different-content") {
      return postJson[currentTeam]?.[currentPlatform] || {};
    }
    return {};
  }, [contentConfig, postJson, currentPlatform, currentTeam]);

  const validateteamsJson = useCallback(
    (teamKey) => {
      const currentTeamJson =
        contentConfig === "same-content" ? postJson : postJson[teamKey];

      const currentTeamMedia =
        mediaConfig === "upload-media"
          ? uploadMediaConfig === "same-media"
            ? fileListState || {}
            : fileListState[teamKey] || {}
          : {};

      for (const platform of Object.keys(currentTeamJson || {})) {
        const rules = postRequiredSchema[platform]?.required;
        if (!rules) continue;

        for (const rule of rules) {
          // CASE 1: Simple string rule
          if (typeof rule === "string") {
            if (rule === "media") {
              if (mediaConfig === "upload-media") {
                if (!currentTeamMedia?.[platform]?.length) return false;
              }
              // Skip if mediaConfig is "no-media"
            } else {
              const value = currentTeamJson?.[platform]?.[rule];
              if (
                !value ||
                (typeof value === "string" && !value.trim().length)
              ) {
                return false;
              }
            }
          }

          // CASE 2: OR condition
          else if (typeof rule === "object" && rule.or) {
            // Special case: when mediaConfig is "no-media", and 'text' is one of the OR conditions — text is mandatory
            if (mediaConfig === "no-media" && rule.or.includes("text")) {
              const text = currentTeamJson?.[platform]?.text;
              if (typeof text !== "string" || !text.trim().length) return false;
            } else {
              // In all other cases, at least one of the OR conditions must pass
              const isOrValid = rule.or.some((condition) => {
                if (condition === "media") {
                  if (mediaConfig === "upload-media") {
                    if (!currentTeamMedia?.[platform]?.length) return false;
                  }
                  return true; // Media condition is valid
                } else {
                  const text = currentTeamJson?.[platform]?.[condition];
                  return typeof text === "string" && text.trim().length > 0;
                }
              });

              if (!isOrValid) return false;
            }
          }
        }
      }

      return true;
    },
    [contentConfig, postJson, mediaConfig, uploadMediaConfig, fileListState]
  );

  const isDisabledStep2 = useCallback(() => {
    if (contentConfig === "same-content") {
      return !validateteamsJson(null);
    } else if (contentConfig === "different-content") {
      return !selectedTeams.every((team) => validateteamsJson(team));
    }
    return true;
  }, [contentConfig, validateteamsJson, selectedTeams]);

  return (
    <>
      <Layout
        hasSider
        style={{
          minHeight: "100vh",
          zoom: 0.9,
        }}
        className="site-layout w-full"
      >
        <Layout>
          <Header
            className="px-0 top-0 sticky z-10 h-[70px]"
            style={{
              backgroundColor: "#FFFFFF",
            }}
          >
            <Row
              align="middle"
              justify="space-between"
              className="mx-5 md:mx-24"
            >
              <Col span={10} md={6} className="center flex">
                <Image
                  style={{
                    cursor: "pointer",
                    objectFit: "contain",
                  }}
                  className="!w-44 md:!w-48"
                  preview={false}
                  src={logo}
                  onClick={() => {
                    subscriptionPlan &&
                      ["cancelled", "active"].includes(
                        subscriptionPlan?.status
                      ) &&
                      navigate(`${appRoot}`);
                  }}
                />
              </Col>

              <Col
                span={14}
                md={10}
                style={{
                  display: "flex",
                  justifyContent: "end",
                  alignItems: "center",
                }}
              >
                <Avatar
                  size={36}
                  className="bg-[#374151] shadow-lg cursor-pointer"
                  shape="circle"
                  onClick={() => {
                    setVisible(true);
                  }}
                >
                  <div className="font-semibold text-sm">
                    {user_details?.name?.charAt(0).toUpperCase()}
                  </div>
                </Avatar>
              </Col>
            </Row>
          </Header>
          <Content className="sm:py-[30px] sm:px-[90px] p-[20px]">
            <div className="flex flex-col gap-[10px]">
              <div className="sm:text-3xl text-xl font-semibold text-[#020817]">
                Manage Media Across Teams
              </div>
              <div className="text-base font-normal text-[#6b7280]">
                Easily upload or clean up media across multiple teams with just
                a few clicks.
              </div>
            </div>

            <Steps
              size="default"
              className="my-10"
              current={step}
              items={[
                {
                  title: "Social Media",
                },
                {
                  title: "Configuration",
                },
                {
                  title: "Post Configuration",
                },
              ]}
            />

            {step === 0 && (
              <Card
                className="rounded-[30px]"
                styles={{
                  body: { padding: 35 },
                }}
              >
                <div className="font-medium text-lg">Select Social Media</div>

                {/* select all check box */}
                <div className="flex items-center gap-3 my-8">
                  <Checkbox
                    checked={
                      Object.keys(selectedPlatform).length ===
                      Object.keys(socialMediaIcons).length
                    }
                    className="custom-checkbox"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedPlatform({ ...manualPostJason });
                      } else {
                        setSelectedPlatform([]);
                      }
                    }}
                  />
                  <div className="text-base font-normal text-[#6B7280]">
                    Select All
                  </div>
                </div>

                <div className="flex flex-wrap gap-5">
                  {Object.keys(socialMediaIcons)?.map((md) => (
                    <div
                      key={`platform_item_${md}`}
                      className={`flex items-center gap-[8px] cursor-pointer py-[10px] px-3  border-solid ${
                        selectedPlatform?.hasOwnProperty(md)
                          ? "border-[#A855F7] bg-[#FAF5FF]"
                          : "border-[#d9dbdf]"
                      } border-[1px] rounded-[10px] `}
                      onClick={() => {
                        if (selectedPlatform[md]) {
                          delete selectedPlatform[md];
                          setSelectedPlatform({
                            ...selectedPlatform,
                          });
                        } else {
                          setSelectedPlatform({
                            ...selectedPlatform,
                            [md]: manualPostJason[md],
                          });
                        }
                      }}
                    >
                      <Avatar
                        key={`avtar_${md}`}
                        src={socialMediaIcons[md]}
                        size={25}
                        //   onClick={() => handleSelect(md)}
                      />
                      <div className="text-base font-normal capitalize">
                        {md?.toLocaleLowerCase()}
                      </div>
                    </div>
                  ))}
                </div>
                {(selectedPlatform?.hasOwnProperty("PINTEREST") ||
                  selectedPlatform?.hasOwnProperty("REDDIT")) && (
                  <Alert
                    type="warning"
                    className="mt-6 rounded-lg border-0 bg-[#FFF7ED] text-[#B45309] font-medium w-fit"
                    showIcon
                    description={
                      <span>
                        <b>Setup Reminder:</b> Before using Bulk Post with{" "}
                        <b>Reddit</b> or <b>Pinterest</b>, ensure you've
                        configured <b>Subreddit</b> or <b>Board Name</b> in Team
                        Settings.
                      </span>
                    }
                  />
                )}
              </Card>
            )}
            {step === 1 && (
              <Card
                className="rounded-[30px]"
                styles={{ body: { padding: 35 } }}
              >
                <div className="flex items-start gap-[30px] w-full max-md:flex-col max-md:gap-5 max-sm:gap-[15px]">
                  {/* Content Configuration */}
                  <div className="flex flex-col items-start gap-[15px] flex-[1_0_0] mb-8">
                    <legend className="flex items-center gap-2.5">
                      <h2 className="text-black text-lg font-medium leading-[21.6px] max-md:text-base max-sm:text-sm m-0">
                        Content Configuration
                      </h2>
                      <Popover
                        content="Easily manage teams, set rules, and connect social accounts in one place."
                        trigger="hover"
                        className="cursor-pointer"
                      >
                        <InfoCircleOutlined className="text-[#9CA8BC]" />
                      </Popover>
                    </legend>

                    <Radio.Group
                      value={contentConfig}
                      onChange={(e) => setContentConfig(e.target.value)}
                      className="flex items-start gap-10 max-md:flex-wrap max-md:gap-5 max-sm:gap-[15px]"
                    >
                      {contentOptions.map((option) => (
                        <Radio
                          key={option.value}
                          value={option.value}
                          style={{ display: "flex", alignItems: "center" }}
                        >
                          <span
                            className={`text-base font-normal leading-[19.2px] max-md:text-sm max-sm:text-xs ${
                              contentConfig === option.value
                                ? "text-[#020817]"
                                : "text-gray-600"
                            }`}
                          >
                            {option.label}
                          </span>
                        </Radio>
                      ))}
                    </Radio.Group>
                  </div>

                  {/* Media Configuration */}
                  <div className="flex flex-col items-start gap-[15px] flex-[1_0_0] mb-8">
                    <legend className="flex items-center gap-2.5">
                      <h2 className="text-black text-lg font-medium leading-[21.6px] max-md:text-base max-sm:text-sm m-0">
                        Media Configuration
                      </h2>
                      <Popover
                        content="Easily manage teams, set rules, and connect social accounts in one place."
                        trigger="hover"
                        className="cursor-pointer"
                      >
                        <InfoCircleOutlined className="text-[#9CA8BC]" />
                      </Popover>
                    </legend>

                    <Radio.Group
                      value={mediaConfig}
                      onChange={(e) => setMediaConfig(e.target.value)}
                      className="flex items-start gap-10 max-md:flex-wrap max-md:gap-5 max-sm:gap-[15px]"
                    >
                      {mediaOptions.map((option) => (
                        <Radio
                          key={option.value}
                          value={option.value}
                          className="flex items-center"
                        >
                          <span
                            className={`text-base font-normal leading-[19.2px] max-md:text-sm max-sm:text-xs ${
                              mediaConfig === option.value
                                ? "text-[#020817]"
                                : "text-gray-600"
                            }`}
                          >
                            {option.label}
                          </span>
                        </Radio>
                      ))}
                    </Radio.Group>
                  </div>
                </div>

                {includesPlatform(selectedPlatform, [
                  "YOUTUBE",
                  "TIKTOK",
                  "INSTAGRAM",
                  "PINTEREST",
                ]) &&
                  mediaConfig === "no-media" && (
                    <Alert
                      type="warning"
                      className="mb-6 rounded-lg border-0 bg-[#FFF7ED] text-[#B45309] font-medium w-fit"
                      showIcon
                      description={
                        <span>
                          <b>Media Required:</b> You've selected <b>YouTube</b>/
                          <b>TikTok</b>/<b>Instagram</b>/<b>Pinterest</b>.
                          Please choose either <b>'Upload Media'</b> or{" "}
                          <b>'Randomized Media'</b> — a video file is required
                          to post on these platforms.
                        </span>
                      }
                    />
                  )}

                {/* Upload Media Configuration */}
                <div
                  className={`flex items-start gap-[30px] w-full max-md:flex-col max-md:gap-5 max-sm:gap-[15px] mb-2 ${
                    mediaConfig === "upload-media" ? "mb-2" : "mb-5"
                  }`}
                >
                  {mediaConfig === "upload-media" && (
                    <div className="flex flex-col items-start gap-[15px] w-full">
                      <legend className="flex items-center gap-2.5">
                        <h2 className="text-black text-lg font-medium leading-[21.6px] max-md:text-base max-sm:text-sm m-0">
                          Upload Media Configuration
                        </h2>
                        <Popover
                          content="Easily manage teams, set rules, and connect social accounts in one place."
                          trigger="hover"
                          className="cursor-pointer"
                        >
                          <InfoCircleOutlined className="text-[#9CA8BC]" />
                        </Popover>
                      </legend>

                      <Radio.Group
                        value={uploadMediaConfig}
                        onChange={(e) => {
                          setFileList({});
                          setUploadMediaConfig(e.target.value);
                        }}
                        className="flex items-start gap-10 max-md:flex-wrap max-md:gap-5 max-sm:gap-[15px]"
                      >
                        {uploadMediaOptions.map((option) => (
                          <Radio
                            key={option.value}
                            value={option.value}
                            className="flex items-center"
                          >
                            <span
                              className={`text-base font-normal leading-[19.2px] max-md:text-sm max-sm:text-xs ${
                                uploadMediaConfig === option.value
                                  ? "text-[#020817]"
                                  : "text-gray-600"
                              }`}
                            >
                              {option.label}
                            </span>
                          </Radio>
                        ))}
                      </Radio.Group>
                    </div>
                  )}

                  <div className="flex flex-col items-start gap-[15px] w-full">
                    <legend className="flex items-center gap-2.5">
                      <h2 className="text-black text-lg font-medium leading-[21.6px] max-md:text-base max-sm:text-sm m-0">
                        Schedule Post
                      </h2>
                      <Popover
                        content="Easily manage teams, set rules, and connect social accounts in one place."
                        trigger="hover"
                        className="cursor-pointer"
                      >
                        <InfoCircleOutlined className="text-[#9CA8BC]" />
                      </Popover>
                    </legend>

                    <DatePicker
                      showTime={{ format: "HH:mm" }}
                      format="YYYY-MM-DD HH:mm"
                      placeholder="Schedule Post"
                      disabledDate={(current) =>
                        current && current < moment().startOf("day")
                      }
                      popupClassName="in-mobile"
                      className="sm:max-w-52 w-full"
                      onChange={onChange}
                      suffixIcon={<CalendarClock size={22} color="#828997" />}
                    />
                  </div>
                </div>

                {/* Team Selector */}
                <section className="flex flex-col items-start gap-[15px] flex-[1_0_0] w-full">
                  <h2 className="text-black text-lg font-medium leading-[21.6px] max-md:text-base max-sm:text-sm m-0">
                    Select Teams
                  </h2>

                  <div className="flex h-12 mb-8 items-center gap-[13px] w-full border box-border p-[13px] rounded-[10px] border-solid border-[#D9DBDF] max-sm:h-10 max-sm:p-2.5">
                    <Input
                      type="text"
                      placeholder="Search team"
                      prefix={<Search strokeWidth={1.5} color="#6b7280" />}
                      value={searchValue}
                      onChange={(e) => setSearchValue(e.target.value)}
                      className="border-0 p-0 h-auto focus:shadow-none placeholder:text-[#D9DBDF]"
                      style={{ boxShadow: "none" }}
                    />
                  </div>
                </section>
                <Row
                  gutter={[16, 10]}
                  className="overflow-y-auto pr-2 custom-scroll"
                  style={{ maxHeight: "calc(100vh - 700px)" }}
                >
                  {Object.values(teams).map((team) => (
                    <Col
                      span={24}
                      xs={24}
                      sm={24}
                      md={12}
                      lg={12}
                      xl={8}
                      xxl={8}
                      key={`teamitem_${team.id}`}
                    >
                      <div className="flex items-center gap-2">
                        <Checkbox
                          className="custom-checkbox"
                          checked={selectedTeams.includes(team.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedTeams((pre) => [...pre, team.id]);
                            } else {
                              setSelectedTeams((pre) =>
                                pre.filter((id) => id !== team.id)
                              );
                            }
                          }}
                          disabled={API?.isLoading}
                        />
                        <div className="truncate text-base text-[#020817] w-full">
                          {team.name}
                        </div>
                      </div>
                    </Col>
                  ))}
                </Row>
              </Card>
            )}
            {step === 2 && (
              <Card
                className="rounded-[30px]"
                styles={{ body: { padding: 35 } }}
              >
                <div className="flex gap-8 max-md:flex-col max-md:gap-5 max-h-[calc(100vh-200px)] max-md:max-h-none overflow-hidden max-md:overflow-visible">
                  {/* Team List Section */}
                  <section className="flex w-full max-w-[400px] lg:w-[400px] md:w-[320px] sm:w-full flex-col items-start gap-[15px] shrink-0 self-stretch max-lg:max-w-full max-md:w-full max-md:mb-6">
                    <h2 className="text-black text-lg font-medium leading-[21.6px] max-sm:text-base m-0">
                      Selected Teams ({selectedTeams.length})
                    </h2>
                    <div className="flex items-start gap-3.5 flex-[1_0_0] self-stretch max-md:flex-col max-md:gap-2.5 overflow-hidden">
                      <div
                        className="flex flex-col items-start gap-2.5 flex-[1_0_0] self-stretch overflow-y-auto pr-2 custom-scroll"
                        style={{
                          scrollbarWidth: "thin",
                          scrollbarColor: "#cbd5e1 transparent",
                          maxHeight: "calc(100vh - 450px)",
                        }}
                      >
                        {selectedTeams.map((team, index) => (
                          <div
                            key={`selecetdTeam_${index}`}
                            className={`flex items-center gap-2.5 self-stretch border p-3 rounded-lg border-solid transition-all duration-200 cursor-pointer min-h-[48px] ${
                              currentTeam === team ||
                              (contentConfig === "same-content" &&
                                uploadMediaConfig !== "different-media")
                                ? "border-[#A855F7] bg-[#FAF5FF]"
                                : "border-[#D9DBDF]"
                            } ${
                              validateteamsJson(team) &&
                              "!bg-[#A855F7] !text-white"
                            }`}
                            onClick={() =>
                              (contentConfig === "different-content" ||
                                uploadMediaConfig === "different-media") &&
                              setCurrentTeam(team)
                            }
                          >
                            <div
                              className={`flex-[1_0_0] overflow-hidden text-ellipsis whitespace-nowrap text-sm font-normal leading-[16.8px] max-sm:text-[13px]`}
                              title={teams[team]?.name}
                            >
                              {teams[team]?.name}
                            </div>
                          </div>
                        ))}
                        {teams.length === 0 && (
                          <div className="flex items-center justify-center p-8 text-center text-gray-500">
                            <div>
                              <div className="text-lg mb-2">
                                No teams selected
                              </div>
                              <div className="text-sm">
                                Please select teams from the previous step
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </section>

                  {/* Main Content Section */}
                  <section className="flex flex-col items-start gap-[30px] flex-[1_0_0] self-stretch max-md:w-full overflow-y-auto max-h-full">
                    {/* Social Account Selector */}
                    <div className="flex flex-wrap items-center gap-4 w-full max-lg:flex-col max-lg:items-start max-lg:gap-4">
                      <h3 className="text-black text-lg font-medium leading-[21.6px] max-sm:text-base m-0 shrink-0">
                        Select Social Accounts:
                      </h3>
                      <div className="flex flex-wrap items-center gap-4">
                        {Object.keys(selectedPlatform)?.map((md) => (
                          <Avatar
                            key={`selectedplatform_${md}`}
                            src={socialMediaIcons?.[md]}
                            size={44}
                            onClick={() => setCurrentPlatform(md)}
                            className={`${
                              currentPlatform === md
                                ? "border-[#a855f7] bg-[#faf5ff]"
                                : "border-[#d9dbdf]"
                            } border-[1px] p-2 bg-transparent cursor-pointer`}
                          />
                        ))}
                      </div>
                    </div>

                    {/* Content Form */}

                    <Form
                      key={`${currentPlatform}-${currentTeam}-${contentConfig}`}
                      form={form}
                      layout="vertical"
                      className="flex flex-col items-start gap-[20px] flex-[1_0_0] self-stretch w-full"
                      onValuesChange={(changedValues) => {
                        // Update postJson based on content configuration
                        if (contentConfig === "same-content") {
                          // For same content, update the platform directly in postJson
                          setPostJson((prev) => ({
                            ...prev,
                            [currentPlatform]: {
                              ...prev[currentPlatform],
                              ...changedValues,
                            },
                          }));
                        } else if (contentConfig === "different-content") {
                          // For different content, update the specific team's platform data
                          setPostJson((prev) => ({
                            ...prev,
                            [currentTeam]: {
                              ...prev[currentTeam],
                              [currentPlatform]: {
                                ...prev[currentTeam]?.[currentPlatform],
                                ...changedValues,
                              },
                            },
                          }));
                        }
                      }}
                    >
                      {/* Render form fields based on platform schema */}
                      {currentPlatform && manualPostJason[currentPlatform] && (
                        <>
                          {/* Text field - always present */}
                          {manualPostJason[currentPlatform].hasOwnProperty(
                            "text"
                          ) && (
                            <Form.Item
                              name="text"
                              rules={[
                                {
                                  required:
                                    postValidationRules[currentPlatform]?.text
                                      ?.required,
                                  message: "Please enter text!",
                                },
                                {
                                  whitespace: true,
                                  message:
                                    "Input cannot be empty or spaces only",
                                },
                                {
                                  max: postValidationRules[currentPlatform]
                                    ?.text?.max,
                                  message: `Max length is ${postValidationRules[currentPlatform]?.text?.max} characters!`,
                                },
                              ]}
                              className="w-full"
                            >
                              <div className="flex flex-col items-start gap-2.5 w-full">
                                <label
                                  htmlFor="text-field"
                                  className="text-black text-lg font-medium leading-[21.6px] max-sm:text-base capitalize"
                                >
                                  {manualPostJason[
                                    currentPlatform
                                  ].hasOwnProperty("description")
                                    ? `${currentPlatform?.toLowerCase()} Title`
                                    : `${currentPlatform?.toLowerCase()} Content`}{" "}
                                  <span className="text-red-500">*</span>
                                </label>
                                {manualPostJason[
                                  currentPlatform
                                ].hasOwnProperty("description") ? (
                                  <Input
                                    id="text-field"
                                    placeholder="Enter title..."
                                    className="!border-[#D9DBDF] !rounded-[15px] !p-4 text-base font-normal leading-[19.2px] max-sm:text-sm max-sm:!p-3 w-full"
                                    style={{
                                      fontSize: "16px",
                                      lineHeight: "19.2px",
                                    }}
                                    value={getCurrentFormData()?.text}
                                    size="large"
                                    maxLength={
                                      postValidationRules[currentPlatform]?.text
                                        ?.max
                                    }
                                  />
                                ) : (
                                  <TextArea
                                    id="text-field"
                                    placeholder="Enter your content here..."
                                    className="!border-[#D9DBDF] !rounded-[15px] !p-[6px] text-base font-normal leading-[19.2px] max-sm:text-sm max-sm:!p-3 !min-h-[140px] w-full resize-none"
                                    style={{
                                      fontSize: "16px",
                                      lineHeight: "19.2px",
                                    }}
                                    autoSize={false}
                                    value={getCurrentFormData()?.text}
                                    showCount
                                    maxLength={
                                      postValidationRules[currentPlatform]?.text
                                        ?.max
                                    }
                                  />
                                )}
                              </div>
                            </Form.Item>
                          )}

                          {/* Description field - only if platform has description */}
                          {manualPostJason[currentPlatform].hasOwnProperty(
                            "description"
                          ) && (
                            <Form.Item
                              name="description"
                              rules={[
                                {
                                  required:
                                    postValidationRules[currentPlatform]
                                      ?.description?.required,
                                  message: "Please enter description!",
                                },
                                {
                                  whitespace: true,
                                  message:
                                    "Input cannot be empty or spaces only",
                                },
                                {
                                  max: postValidationRules[currentPlatform]
                                    ?.description?.max,
                                  message: `Max length is ${postValidationRules[currentPlatform]?.description?.max} characters!`,
                                },
                              ]}
                              className="w-full"
                            >
                              <div className="flex flex-col items-start gap-2.5 w-full">
                                <label
                                  htmlFor="description-field"
                                  className="text-black text-lg font-medium leading-[21.6px] max-sm:text-base capitalize"
                                >
                                  {currentPlatform.toLowerCase()} Description{" "}
                                  <span className="text-red-500">*</span>
                                </label>
                                <TextArea
                                  id="description-field"
                                  placeholder="Enter description here..."
                                  className="!border-[#D9DBDF] !rounded-[15px] !p-[6px] text-base font-normal leading-[19.2px] max-sm:text-sm max-sm:!p-3 !min-h-[140px] w-full resize-none"
                                  style={{
                                    fontSize: "16px",
                                    lineHeight: "19.2px",
                                  }}
                                  autoSize={false}
                                  value={getCurrentFormData()?.description}
                                  showCount
                                  maxLength={
                                    postValidationRules[currentPlatform]
                                      ?.description?.max
                                  }
                                />
                              </div>
                            </Form.Item>
                          )}
                        </>
                      )}
                    </Form>

                    {/* Media Upload */}
                    {mediaConfig === "upload-media" && (
                      <div className="flex flex-col items-start gap-[15px] self-stretch w-full">
                        <h3 className="text-black text-lg font-medium leading-[21.6px] max-sm:text-base m-0">
                          Add Media
                        </h3>
                        <div className="max-h-[180px] md:max-h-[320px] overflow-y-auto pr-2 custom-scroll w-full">
                          <div className="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 xxl:grid-cols-8 gap-3 md:gap-4 min-h-24 w-full">
                            <Upload
                              listType="picture-card"
                              multiple
                              customRequest={({ file }) =>
                                handleUploadChange({ file })
                              }
                              showUploadList={false}
                              beforeUpload={beforeUpload}
                              disabled={API?.isLoading}
                            >
                              <div className="flex flex-col items-center justify-center">
                                <Plus
                                  strokeWidth={1.5}
                                  color="#6b7280"
                                  size={20}
                                />
                                <div className="text-sm font-normal">
                                  Upload
                                </div>
                              </div>
                            </Upload>
                            {(uploadMediaConfig === "same-media"
                              ? fileListState?.[currentPlatform] || []
                              : fileListState?.[currentTeam]?.[
                                  currentPlatform
                                ] || []
                            ).map((item) => (
                              <Card
                                key={item.id}
                                className="relative p-0 overflow-hidden"
                                styles={{ body: { padding: 4 } }}
                              >
                                <div className="relative group w-full h-20 md:h-24">
                                  {item.file?.type !== "video/mp4" ? (
                                    <img
                                      src={item.preview}
                                      alt="preview"
                                      className="object-cover w-full h-full rounded-md"
                                    />
                                  ) : (
                                    <video
                                      src={item.preview}
                                      className="object-cover w-full h-full rounded-md"
                                      controls={false}
                                    />
                                  )}

                                  {/* Type icon */}
                                  {item.file?.type !== "video/mp4" ? (
                                    <ImageIcon
                                      strokeWidth={1.5}
                                      size={18}
                                      color="#fff"
                                      className="top-1 right-1 absolute bg-black/50 rounded-[4px] p-[2px]"
                                    />
                                  ) : (
                                    <Play
                                      strokeWidth={1.5}
                                      color="#fff"
                                      size={14}
                                      className="top-1 right-1 absolute bg-black/50 rounded-[4px] p-[4px]"
                                    />
                                  )}

                                  {/* Hover controls */}
                                  {!API.isLoading && (
                                    <div className="absolute w-full h-full top-0 flex items-center justify-center gap-2 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-md">
                                      <Eye
                                        strokeWidth={1.5}
                                        size={20}
                                        color="#fff"
                                        className="cursor-pointer"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          setOpenPreview({
                                            url: item?.preview,
                                            type:
                                              item?.file?.type !== "video/mp4"
                                                ? "image"
                                                : "video",
                                            name: item.file?.name,
                                            size: item.file?.size,
                                            width: item.file?.width,
                                            height: item.file?.height,
                                          });
                                        }}
                                      />
                                      <Popconfirm
                                        title="Delete file"
                                        description="Are you sure you want to delete this file?"
                                        icon={
                                          <Info
                                            strokeWidth={1.5}
                                            size={16}
                                            color="red"
                                            className="mt-[2px] me-1"
                                          />
                                        }
                                        okText="Yes"
                                        cancelText="No"
                                        onConfirm={(e) => {
                                          e.stopPropagation();
                                          handleDelete(item.id);
                                        }}
                                        onCancel={(e) => e.stopPropagation()}
                                      >
                                        <Trash2
                                          strokeWidth={1.5}
                                          size={20}
                                          color="#fff"
                                          className="cursor-pointer"
                                          onClick={(e) => e.stopPropagation()}
                                        />
                                      </Popconfirm>
                                    </div>
                                  )}

                                  {/* Upload progress bar */}
                                  {item.uploading && (
                                    <div className="absolute top-1/2 w-[80%] left-[10%] h-[6px] bg-gray-200">
                                      <div
                                        className="bg-purple-500 h-full transition-all duration-300"
                                        style={{ width: `${item.progress}%` }}
                                      />
                                    </div>
                                  )}
                                </div>
                              </Card>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </section>
                </div>
              </Card>
            )}
            <Row>
              <Col span={24} xs={24} sm={12} md={12} lg={8} className="mt-8">
                <div className="flex gap-4 w-full">
                  <GradientButton
                    className="w-1/2 "
                    disabled={
                      step === 0
                        ? !Object.keys(selectedPlatform).length
                        : step === 1
                        ? !(
                            25 > selectedTeams.length &&
                            selectedTeams.length >= 2
                          ) ||
                          !contentConfig ||
                          !mediaConfig ||
                          !postDate ||
                          (mediaConfig === "upload-media" && !uploadMediaConfig)
                        : isDisabledStep2()
                    }
                    buttonText={step === 2 ? "Submit" : "Next"}
                    loading={API?.isLoading}
                    onClick={() => (step === 2 ? onSubmit() : onNext())}
                  />
                  <Button
                    className="w-1/2"
                    onClick={() => {
                      step === 0
                        ? navigate(appRoot)
                        : setStep((pre) => pre - 1);
                    }}
                  >
                    Back
                  </Button>
                </div>
              </Col>
            </Row>
          </Content>
        </Layout>
      </Layout>
      <RightDrawer
        user={user_details}
        visible={visible}
        setVisible={setVisible}
        setRefresh={props?.setRefresh}
      />
      <MediaPreview open={openPreview} setOpen={setOpenPreview} />
    </>
  );
};

export default BulkPost;
