import {
  Card,
  Select,
  Checkbox,
  Upload,
  Popconfirm,
  Spin,
  Modal,
  Button,
  Image,
  notification,
} from "antd";
import { useEffect, useState } from "react";
import { ChevronDown, Eye, Info, Plus, Trash2 } from "lucide-react";
import MediaPreview from "../../../component/common/MediaPreview";
import useHttp from "../../../hooks/use-http";
import { useParams } from "react-router-dom";
import { apiGenerator } from "../../../util/functions";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { LoadingOutlined } from "@ant-design/icons";
import GradientButton from "../../../component/common/GradientButton";
import { mediaDelete } from "../../../util/image";

const Mediamanagement = () => {
  const API = useHttp();
  const { teamId } = useParams();
  const [fileList, setFileList] = useState([]);
  const [open, setOpen] = useState(null);
  const [mediaType, setMediaType] = useState(null);
  const [status, setStatus] = useState(null);
  const [order, setOrder] = useState(null);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [deleteCModal, setDeleteCModal] = useState(false);

  useEffect(() => {
    if (!teamId) return;

    const payload = { teamId };
    if (mediaType) payload.type = mediaType;
    if (status) payload.status = status;
    if (order) payload.order = order;

    API.sendRequest(
      apiGenerator(CONSTANTS.API.media.getAll),
      (res) => {
        setFileList(res?.data?.data);
      },
      payload
    );
  }, [teamId, mediaType, status, order]);

  const handleUploadChange = ({ file }) => {
    if (!teamId || !file) return;

    const formData = new FormData();
    formData.append("file", file);

    API.sendRequest(
      apiGenerator(CONSTANTS.API.media.add, {}, `?teamId=${teamId}`),
      (res) => {
        if (res?.data) {
          console.log(res.data);
          setFileList((prev) => [...prev, res.data]);
        }
      },
      formData,
      "Media uploaded successfully."
    );
  };

  const handleDelete = (mId) => {
    if (!mId) return;
    API.sendRequest(
      apiGenerator(CONSTANTS.API.media.delete, { mId }),
      (res) => {
        setFileList((prev) => prev.filter((file) => file.id !== mId));
      },
      {},
      "Media deleted successfully."
    );
  };

  const handleSelect = (fileId) => {
    setSelectedFiles((prev) => {
      if (prev.includes(fileId)) {
        return prev.filter((id) => id !== fileId);
      }

      if (prev.length === 10) {
        notification.warning({
          message: "Maximum File Limit Reached",
          description: "You can select up to 10 files",
        });
        return prev;
      }

      return [...prev, fileId];
    });
  };

  const handleSelectAll = (checked) => {
    setSelectedFiles(checked ? fileList.map((file) => file.id) : []);
  };

  const handleMultiDelete = () => {
    if (selectedFiles.length === 0) return;

    API.sendRequest(
      apiGenerator(CONSTANTS.API.media.deleteMultiple),
      (res) => {
        setFileList((prev) =>
          prev.filter((file) => !selectedFiles.includes(file.id))
        );
        setSelectedFiles([]); // Clear selected files after deletion
        setDeleteCModal(false);
      },
      { ids: selectedFiles },
      "Media deleted successfully."
    );
  };

  return (
    <>
      <Spin
        spinning={API?.isLoading}
        indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
      >
        <Card
          className="w-full rounded-[30px]"
          styles={{ body: { padding: 30 } }}
        >
          <div className="flex flex-wrap items-center md:gap-5 gap-3">
            {/* <Checkbox
              className="image-all text-base text-[#6b7280] font-normal w-full md:w-auto"
              checked={
                selectedFiles.length === fileList.length && fileList.length > 0
              }
              onChange={(e) => handleSelectAll(e.target.checked)}
            >
              Select All
            </Checkbox> */}

            <div className="flex flex-wrap items-center gap-3 md:gap-5 w-full md:w-auto">
              <Select
                placeholder="Select"
                value={mediaType}
                allowClear
                onChange={setMediaType}
                size="large"
                className="md:min-w-28 min-w-36 !h-12"
                options={[
                  { value: "image", label: "Image" },
                  { value: "video", label: "Video" },
                  { value: "image,video", label: "All" },
                ]}
                suffixIcon={
                  <ChevronDown size={22} strokeWidth={1.5} color="#828997" />
                }
              />
              <Select
                placeholder="Select"
                value={status}
                allowClear
                size="large"
                onChange={setStatus}
                className="md:min-w-28 min-w-36 !h-12"
                options={[
                  { value: "used", label: "Used" },
                  { value: "unused", label: "Unused" },
                  { value: "used,unused", label: "Both" },
                ]}
                suffixIcon={
                  <ChevronDown size={22} strokeWidth={1.5} color="#828997" />
                }
              />
              <Select
                placeholder="Select Order"
                value={order}
                allowClear
                size="large"
                onChange={setOrder}
                className="md:min-w-40 min-w-44 !h-12"
                options={[
                  { value: "desc", label: "Newest First" },
                  { value: "asc", label: "Oldest First" },
                ]}
                suffixIcon={
                  <ChevronDown size={22} strokeWidth={1.5} color="#828997" />
                }
              />
              {selectedFiles.length > 0 && (
                <GradientButton
                  buttonText="Delete"
                  onClick={() => setDeleteCModal(true)}
                  className="md:px-10 px-12"
                />
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-7 gap-3 md:gap-5 mt-[30px] min-h-36">
            {/* <ImgCrop> */}
            <Upload
              listType="picture-card"
              multiple={true}
              customRequest={({ file }) => handleUploadChange({ file })}
              showUploadList={false}
              beforeUpload={(file) => {
                const imageTypes = ["image/jpeg", "image/jpg", "image/png"];
                const videoTypes = ["video/mp4"];

                const isImage = imageTypes.includes(file.type);
                const isVideo = videoTypes.includes(file.type);

                if (!isImage && !isVideo) {
                  notification.info({
                    message:
                      "Only JPG, JPEG, PNG images and MP4 videos are allowed.",
                    duration: 3,
                  });
                  return false;
                }

                if (isImage) {
                  const isUnderLimit = file.size / 1024 / 1024 <= 25; // Convert bytes to MB
                  if (!isUnderLimit) {
                    notification.info({
                      message: "Image file size must be less than 25MB.",
                      duration: 3,
                    });
                    return false;
                  }
                }

                if (isVideo) {
                  const isUnderLimit = file.size / 1024 / 1024 <= 1024; // 1GB
                  if (!isUnderLimit) {
                    notification.info({
                      message: "Video file size must be less than 1GB.",
                      duration: 3,
                    });
                    return false;
                  }
                }

                return true;
              }}
            >
              <div className="flex flex-col items-center justify-center">
                <Plus strokeWidth={1.5} color="#6b7280" size={20} />
                <div className="text-sm font-normal">Upload</div>
              </div>
            </Upload>

            {/* </ImgCrop> */}

            {fileList.map((file, i) => (
              <Card
                key={`mItem${i}`}
                styles={{
                  body: {
                    padding: 9,
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                  },
                }}
                className={`${
                  selectedFiles?.includes(file?.id)
                    ? "ant-card-custom-m-select bg-[#faf5ff]"
                    : "ant-card-custom"
                }`}
              >
                <div className="relative group w-full">
                  <img
                    src={file?.thumbnailUrl}
                    alt={"mediaurl"}
                    className="object-cover w-full h-24 sm:h-28 md:h-24 rounded-md"
                  />
                  <div className="absolute w-full h-24 sm:h-28 md:h-24 top-0 flex items-center justify-center gap-2 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-md">
                    <Eye
                      strokeWidth={1.5}
                      size={20}
                      color="#fff"
                      className="cursor-pointer"
                      onClick={() => setOpen(file)}
                    />
                    <Popconfirm
                      title="Delete image"
                      description="Are you sure you want to delete this image?"
                      icon={
                        <Info
                          strokeWidth={1.5}
                          size={16}
                          color="red"
                          className="mt-[2px] me-1"
                        />
                      }
                      okText="Yes"
                      cancelText="No"
                      onConfirm={() => handleDelete(file?.id)}
                    >
                      <Trash2
                        strokeWidth={1.5}
                        size={20}
                        color="#fff"
                        className="cursor-pointer"
                      />
                    </Popconfirm>
                  </div>
                </div>
                <div className="flex justify-between items-center w-full mt-[2px] text-sm">
                  <span className="truncate max-w-[70%] text-xs font-normal">
                    {file?.name}
                  </span>
                  <Checkbox
                    className="w-[14px] h-[14px]"
                    checked={selectedFiles?.includes(file.id)}
                    onChange={() => handleSelect(file.id)}
                  />
                </div>
              </Card>
            ))}
          </div>
        </Card>
      </Spin>
      <MediaPreview open={open} setOpen={setOpen} />
      <Modal
        open={deleteCModal}
        closeIcon={false}
        footer={null}
        centered
        onCancel={() => setDeleteCModal(false)}
        maskClosable={false}
        style={{ zoom: 0.9 }}
        width={500}
        className="rounded-[20px] md:rounded-[40px] overflow-hidden p-0 auth-modal"
        styles={{
          content: { padding: 0 },
          body: { padding: "50px" },
          mask: { backgroundColor: "rgba(0, 0, 0, 0.8)" },
        }}
      >
        <div className="flex flex-col mb-[50px]">
          <div className="flex justify-center">
            <Image src={mediaDelete} width={90} preview={false} />
          </div>
          <div className="text-4xl font-semibold text-center mt-[30px]">
            Delete Media File?
          </div>
          <div className="text-lg text-[#6B7280] text-center mt-2">
            Are you sure you want to delete the selected media files? They will
            be gone forever!
          </div>
        </div>
        <div className="flex gap-4">
          <GradientButton
            htmlType="submit"
            className="w-1/2 "
            buttonText="Yes"
            loading={API.isLoading}
            onClick={handleMultiDelete}
          />
          <Button
            className="w-1/2"
            onClick={() => {
              setSelectedFiles([]);
              setDeleteCModal(false);
            }}
          >
            No
          </Button>
        </div>
      </Modal>
    </>
  );
};

export default Mediamanagement;
