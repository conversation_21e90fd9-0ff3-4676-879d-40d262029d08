import { Form, Image, Modal, notification } from "antd";
import axios from "axios";
import React, { useEffect, useState } from "react";
import right from "../../../asset/image/Group 21.svg";
import { InputOTP } from "antd-input-otp";
import GradientButton from "../../../component/common/GradientButton";
import useHttp from "../../../hooks/use-http";
import { useNavigate } from "react-router-dom";
import TimeoutOTP from "../../../component/common/TimeoutOTP";
import { appRoot } from "../../../util/constant/CONSTANTS";
import { setAuthDetails } from "../../../util/API/authStorage";

const Verification = ({
  open = false,
  close = () => {},
  email = null,
  token = null,
  setToken,
}) => {
  const API = useHttp();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [step, setStep] = useState("otp");
  const [load, setLoad] = useState(false);

  const onCloseModal = () => {
    setStep("otp");
    close();
    navigate("/");
  };

  useEffect(() => {
    localStorage.setItem("time", 120);
    return () => {
      localStorage.removeItem("time");
    };
  }, []);

  useEffect(() => {
    if (step !== "success") return;

    const timeoutId = setTimeout(() => {
      window?.location?.assign(appRoot);
    }, 5000);

    return () => clearTimeout(timeoutId);
  }, [step]);

  const handleOTPVerification = async (value) => {
    if (!token) return;
    const payload = {
      otp: value?.otp?.join(""),
    };
    try {
      setLoad(true);
      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/users/verify`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${token}`, // Pass token in headers
          },
        }
      );
      if (response?.status === 200) {
        setStep("success");
        setLoad(false);
        setAuthDetails(response?.data?.token);
        notification.success({
          message: response?.data?.message,
          duration: "3",
        });
      }
    } catch (error) {
      // return notification.warning({
      //   message: error?.response?.data?.message,
      //   duration: "3",
      // });
      setLoad(false);
      setTimeout(() => {
        form.setFields([
          {
            name: "otp",
            errors: [error?.response?.data?.message || "Invalid OTP!"],
          },
        ]);
      }, 100);
    }
  };

  const handleResendOTP = async (value) => {
    const payload = {};
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/users/resendOtp`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response?.status === 200) {
        notification.success({
          message: response?.data?.message,
          duration: "3",
        });
        setToken(response?.data?.token);
      }
    } catch (error) {
      return notification.warning({
        message: error?.response?.data?.message,
        duration: "3",
      });
    }
  };

  const EnterOtp = () => {
    return (
      <>
        <div className="flex flex-col gap-3 mb-10">
          <div className="text-4xl font-semibold text-center">
            Enter verification code
          </div>
          <div className="text-lg text-[#6B7280] text-center">
            The verification code has been sent to your email {email}
          </div>
        </div>
        <Form
          form={form}
          name="otp_form"
          className="login-form"
          onFinish={handleOTPVerification}
          validateTrigger="onBlur"
        >
          <Form.Item
            name="otp"
            className="mb-4"
            rules={[
              { required: true, message: "Please enter the OTP!" },
              {
                validator: (_, value) =>
                  value && value.join("").length === 6
                    ? Promise.resolve()
                    : Promise.reject(
                        new Error("OTP must be exactly 6 digits!")
                      ),
              },
            ]}
          >
            <InputOTP
              length={6}
              inputType="numeric"
              inputClassName="otp-input bg-transparent"
              classNames="w-full"
              inputRegex={/^[0-9]+$/}
              autoFocus
              pattern="[0-9]*"
              inputMode="numeric"
              inputStyle={{
                width: "48px",
                height: "48px",
                fontSize: "16px",
                borderRadius: "10px",
                marginRight: 0,
              }}
            />
          </Form.Item>
          <span className="text-[#788094] text-sm">
            Not received yet? <TimeoutOTP handleResendOTP={handleResendOTP} />
          </span>

          <Form.Item className="mb-0">
            <GradientButton
              htmlType="submit"
              className="w-full mt-[50px]"
              buttonText="Verify Code"
              loading={load}
            />
          </Form.Item>
        </Form>
      </>
    );
  };

  const SuccessMessage = () => {
    return (
      <>
        <div className="flex flex-col mb-[50px]">
          <div className="flex justify-center">
            <Image src={right} width={90} preview={false} />
          </div>
          <div className="text-4xl font-semibold text-center mt-[30px]">
            Verification Complete
          </div>
          <div className="text-lg text-[#6B7280] text-center mt-5">
            Congratulations! 🎉
          </div>
          <div className="text-lg text-[#6B7280] text-center mt-2">
            Your email has been verified. You can now login and access our
            services.
          </div>
        </div>
        <GradientButton
          className="w-full"
          buttonText="Get Started"
          // onClick={onCloseModal}
          onClick={() => window?.location?.assign(appRoot)}
        />
      </>
    );
  };

  return (
    <Modal
      open={open}
      closeIcon={false}
      footer={null}
      centered
      onCancel={close}
      width={500}
      maskClosable={false}
      style={{ zoom: 0.9 }}
      className="rounded-[20px] md:rounded-[40px] overflow-hidden p-0 auth-modal"
      styles={{
        content: { padding: 0 },
        body: { padding: "50px" },
        mask: { backgroundColor: "rgba(0, 0, 0, 0.8)" },
      }}
    >
      {step === "otp" && <EnterOtp />}
      {step === "success" && <SuccessMessage />}
    </Modal>
  );
};

export default Verification;
