import { Button, Form, Image, Input, Modal, notification } from "antd";
import React, { useEffect, useState } from "react";
import em from "../../../asset/logos/em.svg";
import right from "../../../asset/image/Group 22.svg";
import pass from "../../../asset/logos/pass.svg";
import { InputOTP } from "antd-input-otp";
import useHttp from "../../../hooks/use-http";
import { getAuthToken } from "../../../util/API/authStorage";
import CONSTANTS, { appRoot } from "../../../util/constant/CONSTANTS";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import GradientButton from "../../../component/common/GradientButton";
import TimeoutOTP from "../../../component/common/TimeoutOTP";

const ForgetPassword = ({ open = false, close = () => {} }) => {
  const API = useHttp();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [token, setToken] = useState(null);
  const [step, setStep] = useState("email");
  const [email, setEmail] = useState(null);
  const [load, setLoad] = useState(false);

  useEffect(() => {
    const isLogin = getAuthToken() !== undefined && getAuthToken() !== null;
    if (isLogin) {
      navigate(appRoot);
    }
  }, [navigate]);

  useEffect(() => {
    localStorage.setItem("time", 120);
    return () => {
      localStorage.removeItem("time");
    };
  }, []);

  const handleOTPSent = (value) => {
    const payload = { email: value?.email?.toLowerCase()?.trim() };
    setEmail(value?.email?.toLowerCase()?.trim());
    API.sendRequest(
      CONSTANTS.API.forgotPassword.sendEmail,
      (res) => {
        setToken(res?.token);
        setStep("otp");
      },
      payload,
      "OTP sent successfully."
    );
  };

  const onCloseModal = () => {
    setStep("email");
    close();
  };

  const handleChange = (field) => {
    const nameError = form.getFieldError(field);
    if (nameError.length) {
      form.validateFields([field]);
    }
  };

  const GetEmail = () => {
    return (
      <>
        <div className="flex flex-col gap-3 mb-10">
          <div className="text-4xl font-semibold text-center">
            Forgot Password?
          </div>
          <div className="text-lg text-[#6B7280] text-center">
            Enter your registered email to reset your password.
          </div>
        </div>
        <Form
          name="normal_login"
          className="login-form"
          form={form}
          initialValues={{
            remember: true,
          }}
          onFinish={handleOTPSent}
          validateTrigger="onBlur"
        >
          <p className="font-medium text-base reuired-field mb-2 text-[#020817]">
            Email
            <span className="text-red-600 ml-1 ">*</span>
          </p>
          <Form.Item
            name="email"
            className="mb-0"
            rules={[
              {
                required: true,
                message: "Please Enter Your Email!",
              },
              {
                type: "email",
                message: "Please enter a valid email address!",
              },
            ]}
          >
            <Input
              prefix={
                <Image src={em} alt="Email Icon" preview={false} width={22} />
              }
              placeholder="<EMAIL>"
              type="email"
              autoComplete="off"
              onChange={() => handleChange("email")}
            />
          </Form.Item>

          <Form.Item className="mb-0">
            <GradientButton
              htmlType="submit"
              className="w-full mt-[50px]"
              buttonText="Confirm"
              loading={API?.isLoading}
            />
          </Form.Item>
        </Form>
        <Button className="rounded-md w-full mt-[10px]" onClick={onCloseModal}>
          Back To Login
        </Button>
      </>
    );
  };

  const handleOTPVerification = async (value) => {
    const payload = {
      otp: value?.otp?.join(""),
    };
    try {
      setLoad(true);
      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/users/verifyForgotpasswordOtp`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response?.status === 200) {
        notification.success({
          message: response?.data?.message,
          duration: "3",
        });
        setLoad(false);
        setToken(response?.data?.token);
        setStep("password");
      }
    } catch (error) {
      setLoad(false);
      setTimeout(() => {
        form.setFields([
          {
            name: "otp",
            errors: [error?.response?.data?.message || "Invalid OTP!"],
          },
        ]);
      }, 100);
    }
  };

  const handleResendOTP = async (value) => {
    const payload = {};
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/users/resendForgotpasswordOtp`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response?.status === 200) {
        notification.success({
          message: response?.data?.message,
          duration: "3",
        });
        setToken(response?.data?.token);
      }
    } catch (error) {
      return notification.warning({
        message: error?.response?.data?.message,
        duration: "3",
      });
    }
  };

  const EnterOtp = () => {
    return (
      <>
        <div className="flex flex-col gap-3 mb-10">
          <div className="text-4xl font-semibold text-center">
            Enter verification code
          </div>
          <div className="text-lg text-[#6B7280] text-center">
            The verification code has been sent to your email {email}
          </div>
        </div>
        <Form
          form={form}
          name="otp_form"
          className="login-form"
          onFinish={handleOTPVerification}
          validateTrigger="onBlur"
        >
          <Form.Item
            name="otp"
            className="mb-4"
            rules={[
              { required: true, message: "Please enter the OTP!" },
              {
                validator: (_, value) =>
                  value && value.join("").length === 6
                    ? Promise.resolve()
                    : Promise.reject(
                        new Error("OTP must be exactly 6 digits!")
                      ),
              },
            ]}
          >
            <InputOTP
              length={6}
              inputClassName="otp-input bg-transparent"
              classNames="w-full"
              inputType="numeric"
              autoFocus
              onChange={() => handleChange("otp")}
              inputStyle={{
                width: "48px",
                height: "48px",
                fontSize: "16px",
                borderRadius: "10px",
                marginRight: "10px",
              }}
            />
          </Form.Item>
          <span className="text-[#788094] text-sm">
            Not received yet? <TimeoutOTP handleResendOTP={handleResendOTP} />
          </span>

          <Form.Item className="mb-0">
            <GradientButton
              htmlType="submit"
              className="w-full mt-[50px]"
              buttonText="Verify Code"
              loading={load}
            />
          </Form.Item>
        </Form>
      </>
    );
  };

  const handlePasswordReset = async (value) => {
    const payload = {
      password: value?.password,
      confirmPassword: value?.confirmPassword,
    };
    try {
      setLoad(true);
      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/users/resetPassword`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${token}`, // Pass token in headers
          },
        }
      );
      if (response?.status === 200) {
        notification.success({
          message: response?.data?.message,
          duration: "3",
        });
        setLoad(false);
        setStep("success");
      }
    } catch (error) {
      setLoad(false);
      return notification.warning({
        message: error?.response?.data?.message,
        duration: "3",
      });
    }
  };

  const ConfirmPassword = () => {
    return (
      <>
        <div className="flex flex-col gap-3 mb-10">
          <div className="text-4xl font-semibold text-center">New Password</div>
          <div className="text-lg text-[#6B7280] text-center">
            Enter a new password for your account.
          </div>
        </div>
        <Form
          name="reset_password"
          className="login-form"
          form={form}
          initialValues={{
            remember: true,
          }}
          onFinish={handlePasswordReset}
          validateTrigger="onBlur"
        >
          {/* Password Field */}
          <p className="font-medium text-base required-field mb-2 text-[#020817]">
            Password<span className="text-red-600 ml-1">*</span>
          </p>
          <Form.Item
            name="password"
            rules={[
              {
                required: true,
                message: "Please enter your password!",
              },
              {
                pattern:
                  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{8,}$/,
                message:
                  "Must be 8+ chars with uppercase, lowercase, number & special char (@$!%*?&#).",
              },
            ]}
          >
            <Input.Password
              prefix={
                <Image
                  src={pass}
                  alt="Password Icon"
                  preview={false}
                  width={22}
                />
              }
              onChange={() => handleChange("password")}
              placeholder="enter new password"
              autoComplete="off"
            />
          </Form.Item>

          {/* Confirm Password Field */}
          <p className="font-medium text-base text-[#020817] required-field mb-2 mt-4">
            Confirm Password<span className="text-red-600 ml-1">*</span>
          </p>
          <Form.Item
            name="confirmPassword"
            dependencies={["password"]}
            className="mb-0"
            rules={[
              {
                required: true,
                message: "Please confirm your password!",
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("password") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error("Passwords do not match!"));
                },
              }),
            ]}
          >
            <Input.Password
              prefix={
                <Image
                  src={pass}
                  alt="Password Icon"
                  preview={false}
                  width={22}
                />
              }
              onChange={() => handleChange("confirmPassword")}
              placeholder="confirm new password"
              autoComplete="off"
            />
          </Form.Item>

          <Form.Item className="mb-0 mt-[50px]">
            <div className="flex gap-4">
              <GradientButton
                htmlType="submit"
                className="w-1/2 "
                buttonText="Confirm"
                loading={load}
              />
              <Button className="w-1/2" onClick={onCloseModal}>
                Cancel
              </Button>
            </div>
          </Form.Item>
        </Form>
      </>
    );
  };

  const SuccessMessage = () => {
    return (
      <>
        <div className="flex flex-col mb-[50px]">
          <div className="flex justify-center">
            <Image src={right} width={90} preview={false} />
          </div>
          <div className="text-4xl font-semibold text-center mt-[30px]">
            Password Reset Successfully !
          </div>
          <div className="text-lg text-[#6B7280] text-center mt-5">
            Congratulations! 🎉
          </div>
          <div className="text-lg text-[#6B7280] text-center mt-2">
            Your password has been changed. You can now log in with your new
            credentials.
          </div>
        </div>
        <GradientButton
          className="w-full"
          buttonText="Go To Login"
          onClick={onCloseModal}
        />
      </>
    );
  };

  return (
    <Modal
      open={open}
      closeIcon={false}
      footer={null}
      centered
      onCancel={onCloseModal}
      maskClosable={false}
      style={{ zoom: 0.9 }}
      width={500}
      className="rounded-[20px] md:rounded-[40px] overflow-hidden p-0 auth-modal"
      styles={{
        content: { padding: 0 },
        body: { padding: "50px" },
        mask: { backgroundColor: "rgba(0, 0, 0, 0.8)" },
      }}
    >
      {step === "email" && <GetEmail />}
      {step === "otp" && <EnterOtp />}
      {step === "password" && <ConfirmPassword />}
      {step === "success" && <SuccessMessage />}
    </Modal>
  );
};

export default ForgetPassword;
