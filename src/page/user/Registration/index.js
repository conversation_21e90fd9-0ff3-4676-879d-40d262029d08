import { <PERSON><PERSON>, Card, Form, Image, Input, Row, Typography } from "antd";
import React, { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import pass from "../../../asset/logos/pass.svg";
import em from "../../../asset/logos/em.svg";
import logo from "../../../asset/logos/icon.svg";
import org from "../../../asset/logos/org.svg";
import use from "../../../asset/logos/use.svg";
import useHttp from "../../../hooks/use-http";
import GradientButton from "../../../component/common/GradientButton";
import { getAuthToken } from "../../../util/API/authStorage";
import CONSTANTS, { appRoot } from "../../../util/constant/CONSTANTS";
import Verification from "./verification";

const Registration = () => {
  const navigate = useNavigate();
  const API = useHttp();
  const [form] = Form.useForm();
  const [openModal, setOpenModal] = useState(false);
  const [email, setEmail] = useState(null);
  const [token, setToken] = useState(null);

  useEffect(() => {
    const isLogin = getAuthToken() !== undefined && getAuthToken() !== null;
    if (isLogin) {
      navigate(appRoot);
    }
  }, [navigate]);

  const handleNameChange = (field) => {
    const nameError = form.getFieldError(field);
    if (nameError.length) {
      form.validateFields([field]);
    }
  };

  const onSignUp = (value) => {
    const payload = {
      email: value?.email?.toLowerCase()?.trim(),
      name: value?.name?.trim(),
      organizationName: value?.organizationName?.trim(),
      password: value?.password?.trim(),
    };
    setEmail(value?.email?.trim());
    API.sendRequest(
      CONSTANTS.API.signUp.register,
      (res) => {
        setToken(res?.token);
        setOpenModal((pre) => !pre);
      },
      payload
    );
  };

  return (
    <>
      <Row className="h-svh w-full flex justify-center items-center bg-auth fixed overflow-auto">
        <div className="w-[500px] mobile:px-5 py-14 mobile:py-8 mobilescale macair">
          <div className="flex justify-center">
            <Image src={logo} alt="Woo" preview={false} className="!w-72" />
          </div>
          <Card
            className="p-[50px] mobile:p-8 rounded-[20px] md:rounded-[40px] mobile:mt-10 mobile:mb-5 mt-[50px] mb-[30px]"
            styles={{ body: { padding: 0 } }}
          >
            <div>
              <div className="flex flex-col gap-3 mb-10">
                <div className="text-4xl font-bold text-center">Sign Up</div>
                <div className="text-lg text-[#6B7280] text-center">
                  Best platform for your social posts & creating amazing content
                </div>
              </div>
              <Form
                name="login_form"
                className="login-form"
                form={form}
                initialValues={{ remember: true }}
                onFinish={onSignUp}
                validateTrigger={["onBlur"]}
              >
                {/* Name */}
                <p className="font-medium text-base text-[#020817] required-field mb-2">
                  Name<span className="text-red-600 ml-1">*</span>
                </p>
                <Form.Item
                  className="mb-4"
                  name="name"
                  rules={[
                    { required: true, message: "Please Enter Your Name!" },
                    {
                      whitespace: true,
                      message: "Input cannot be empty or spaces only",
                    },
                    {
                      max: 100,
                      message: "Name cannot exceed 100 characters!",
                    },
                  ]}
                >
                  <Input
                    prefix={
                      <Image
                        src={use}
                        alt="User Icon"
                        preview={false}
                        width={22}
                      />
                    }
                    placeholder="enter name"
                    autoComplete="off"
                    maxLength={100}
                    onChange={() => handleNameChange("name")}
                  />
                </Form.Item>

                {/* Email */}
                <p className="font-medium text-base text-[#020817] required-field mb-2">
                  Email<span className="text-red-600 ml-1">*</span>
                </p>
                <Form.Item
                  className="mb-4"
                  name="email"
                  rules={[
                    { required: true, message: "Please Enter Your Email!" },
                    {
                      type: "email",
                      message: "Please enter a valid email address!",
                    },
                  ]}
                >
                  <Input
                    prefix={
                      <Image
                        src={em}
                        alt="Email Icon"
                        preview={false}
                        width={22}
                      />
                    }
                    placeholder="<EMAIL>"
                    type="email"
                    autoComplete="off"
                    onChange={() => handleNameChange("email")}
                  />
                </Form.Item>

                {/* Password */}
                <p className="font-medium text-base text-[#020817] required-field mb-2">
                  Password<span className="text-red-600 ml-1">*</span>
                </p>
                <Form.Item
                  className="mb-4"
                  name="password"
                  rules={[
                    { required: true, message: "Please Enter Your Password!" },
                    {
                      pattern:
                        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{8,}$/,
                      message:
                        "Password must be 8+ chars with uppercase, lowercase, number & special character (@$!%*?&#).",
                    },
                  ]}
                >
                  <Input.Password
                    prefix={
                      <Image
                        src={pass}
                        alt="Password Icon"
                        preview={false}
                        width={22}
                      />
                    }
                    autoComplete="off"
                    placeholder="enter password"
                    onChange={() => handleNameChange("password")}
                  />
                </Form.Item>

                {/* Organization */}
                <p className="font-medium text-base text-[#020817] required-field mb-2">
                  Organization
                </p>
                <Form.Item
                  className="mb-0"
                  name="organizationName"
                  rules={[
                    {
                      whitespace: true,
                      message: "Input cannot be empty or spaces only",
                    },
                  ]}
                >
                  <Input
                    prefix={
                      <Image
                        src={org}
                        alt="Email Icon"
                        preview={false}
                        width={22}
                      />
                    }
                    placeholder="enter organization name "
                    autoComplete="off"
                    onChange={() => handleNameChange("organizationName")}
                  />
                </Form.Item>

                {/* Submit Button */}
                <Form.Item className="mt-10 mb-4">
                  <GradientButton
                    className="w-full"
                    htmlType="submit"
                    buttonText="Sign Up"
                    loading={API?.isLoading}
                  />
                </Form.Item>
              </Form>
            </div>
          </Card>
          <div className="text-base text-center text-[#4B5563]">
            Already have an account?{" "}
            <Link className="text-base cursor-pointer" to={"/"}>
              Login
            </Link>
          </div>
        </div>
      </Row>
      {openModal && (
        <Verification
          open={openModal}
          close={() => setOpenModal((pre) => !pre)}
          email={email}
          token={token}
          setToken={setToken}
        />
      )}
    </>
  );
};

export default Registration;
