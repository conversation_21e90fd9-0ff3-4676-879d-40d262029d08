/* eslint-disable no-debugger */
/* eslint-disable brace-style */
/* eslint-disable comma-dangle */
/* eslint-disable react/jsx-one-expression-per-line */
/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable react/button-has-type */
import React, { useEffect, useState } from "react";
// import { GoogleAuthProvider, signInWithPopup } from "firebase/auth";
import {
  Card,
  Col,
  Divider,
  Image,
  Row,
  Spin,
  Typography,
  notification,
} from "antd";
import bgimage from "../../../asset/image/Login Screen.png";
import { FcGoogle } from "react-icons/fc";
import Title from "antd/es/typography/Title";
import axios from "axios";
import { Link, useLocation, useNavigate } from "react-router-dom";
// import { Helmet } from "react-helmet";
import CONSTANTS, {
  appRoot,
  distributeRoot,
} from "../../../util/constant/CONSTANTS";
import {
  deleteAuthDetails,
  getAuthToken,
  setAuthDetails,
} from "../../../util/API/authStorage";
import useHttp from "../../../hooks/use-http";
import logo from "../../../asset/logos/icon.svg";
// import { auth } from "../../../util/firebase-config";
import useCheckInternet from "../../../hooks/use-checkInternet";
import LoginForm from "./component/login-form";
import OtpForm from "./component/otp-form";
import { removeSpacesFromObject } from "../../../util/functions";

const { Text } = Typography;
let otpToken = "";
const LogIn = () => {
  const [loading, setLoading] = useState(false);
  const [otpSend, setOtpSend] = useState(false);
  const [userEmail, setUserEmail] = useState(null);

  const API = useHttp();
  const sendOtpAPI = useHttp();
  const location = useLocation();
  const urlParams = new URLSearchParams(location?.search);

  useCheckInternet("/noInternet");

  // auth.useDeviceLanguage();
  const navigate = useNavigate();
  useEffect(() => {
    if (urlParams.get("token")) {
      setAuthDetails(urlParams.get("token"));
      window.location.assign(appRoot);
    }
    if (urlParams.get("userRole")) {
      deleteAuthDetails();
      localStorage.clear();
    }

    if (urlParams.get("userToken")) {
      try {
        axios
          .get(
            `${process.env.REACT_APP_BASE_URL}${CONSTANTS.API.auth.getMe.endpoint}`,
            {
              headers: {
                Accept: "application/json",
                Authorization: `Bearer ${urlParams.get("userToken")}`,
              },
            }
          )
          .then(() => {
            setAuthDetails(urlParams.get("userToken"));
            window.location.assign(`${appRoot}`);
          })
          .catch((e) => {
            console.log(e);
          });
      } catch (error) {
        console.log(error);
      }
    }
    const isLogin = getAuthToken() !== undefined && getAuthToken() !== null;

    if (isLogin) {
      localStorage.getItem("userRole") === "Distributer" &&
      !location?.pathname?.includes("distribute")
        ? navigate(distributeRoot)
        : navigate(appRoot);
    }
  }, []);

  const onEmailFormHandler = (values) => {
    setUserEmail(values?.email?.trim());

    sendOtpAPI.sendRequest(
      CONSTANTS.API.auth.loginWithEmail,
      (res) => {
        if (res?.status === "success") {
          otpToken = res?.token;
          setOtpSend(true);
        }
      },
      removeSpacesFromObject(values),
      ""
    );
  };

  const onOtpFormHandler = (payload) => {
    setLoading(true);
    (async () => {
      try {
        const res = await axios.post(
          `${process.env.REACT_APP_BASE_URL}${CONSTANTS.API.auth.verify.endpoint}`,
          { otp: +payload?.otp },
          {
            headers: {
              Accept: "application/json",
              Authorization: `Bearer ${otpToken}`,
            },
          }
        );
        setAuthDetails(res?.data?.token);
        localStorage.setItem("userRole", res?.data?.role);
        window.location.assign(appRoot);
        // window.location.assign("/");
      } catch (error) {
        setLoading(false);
        notification.error({
          message: error?.response?.data?.message,
          duration: "3",
        });
      }
    })();
  };

  return (
    <Row className="h-svh w-full flex justify-center items-center bg-auth fixed overflow-auto">
      <div className="w-[500px] mobile:px-5 py-14 mobile:py-8 mobilescale">
        <div className="flex justify-center">
          <Image src={logo} alt="Woo" preview={false} className="!w-72" />
        </div>
        <Card
          className="p-[50px] mobile:p-8  rounded-[20px] md:rounded-[40px] mobile:mt-10 mobile:mb-5 mt-[50px] mb-[30px]"
          styles={{ body: { padding: 0 } }}
        >
          <LoginForm
            sendOtpAPI={sendOtpAPI}
            onEmailFormHandler={onEmailFormHandler}
          />
        </Card>
        <div className="text-base text-center text-[#4B5563]">
          Don’t have an account?{" "}
          <Link className="text-base cursor-pointer" to={"/registration"}>
            Sign Up
          </Link>
        </div>
      </div>
    </Row>
  );
};
export default LogIn;
