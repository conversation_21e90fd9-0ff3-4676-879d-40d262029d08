import React, { useEffect, useState } from "react";
import { Button, Form, Image, Input } from "antd";
import em from "../../../../asset/logos/em.svg";
import pass from "../../../../asset/logos/pass.svg";
import { useNavigate } from "react-router-dom";
import GradientButton from "../../../../component/common/GradientButton";
import ForgetPassword from "../../Registration/forgetPass";
import useHttp from "../../../../hooks/use-http";
import { getAuthToken, setAuthDetails } from "../../../../util/API/authStorage";
import CONSTANTS, { appRoot } from "../../../../util/constant/CONSTANTS";

const LoginForm = () => {
  const API = useHttp();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [openfpmodal, setOpenfpmodal] = useState(false);

  useEffect(() => {
    const isLogin = getAuthToken() !== undefined && getAuthToken() !== null;
    if (isLogin) {
      navigate(appRoot);
    }
  }, []);

  const validateLogin = (value) => {
    const payload = {
      email: value?.email?.toLowerCase(),
      password: value?.password,
    };
    API.sendRequest(
      CONSTANTS.API.auth.loginWithEmail,
      (res) => {
        setAuthDetails(res?.token);
        window?.location?.assign(appRoot);
      },
      payload,
      "LogIn Successful"
    );
  };

  const handleChange = (field) => {
    const nameError = form.getFieldError(field);
    if (nameError.length) {
      form.validateFields([field]);
    }
  };

  return (
    <>
      <div>
        <div className="flex flex-col gap-3 mb-10">
          <div className="text-4xl font-bold text-center">Welcome Back</div>
          <div className="text-lg text-[#6B7280] text-center">
            Login to your account to stay connected.
          </div>
        </div>
        <Form
          name="normal_login"
          className="login-form"
          form={form}
          initialValues={{
            remember: true,
          }}
          onFinish={validateLogin}
          validateTrigger="onBlur"
        >
          <p className="font-medium reuired-field text-base mb-2 text-[#020817]">
            Email
            <span className="text-red-600 ml-1 ">*</span>
          </p>
          <Form.Item
            name="email"
            className="!mb-4"
            rules={[
              {
                required: true,
                message: "Please Enter Your Email!",
              },
              {
                type: "email",
                message: "Please enter a valid email address!",
              },
            ]}
          >
            <Input
              prefix={
                <Image src={em} alt="Email Icon" preview={false} width={22} />
              }
              placeholder="<EMAIL>"
              type="email"
              autoComplete="off"
              onChange={() => handleChange("email")}
            />
          </Form.Item>
          <p className="font-medium reuired-field text-base mb-2 text-[#020817]">
            Password
            <span className="text-red-600 ml-1 ">*</span>
          </p>
          <Form.Item
            name="password"
            className="mb-2"
            rules={[
              {
                required: true,
                message: "Please Enter Your Password!",
              },
              {
                pattern:
                  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{8,}$/,
                message:
                  "Password must be 8+ chars with uppercase, lowercase, number & special character (@$!%*?&#).",
              },
            ]}
          >
            <Input.Password
              prefix={
                <Image
                  src={pass}
                  alt="Password Icon"
                  preview={false}
                  width={22}
                />
              }
              autoComplete="off"
              placeholder="enter password"
              onChange={() => handleChange("password")}
            />
          </Form.Item>
          <span
            className="text-sm text-[#4B5563] cursor-pointer"
            onClick={() => setOpenfpmodal((pre) => !pre)}
          >
            Forgot password?
          </span>

          <Form.Item className="mt-10 mb-4">
            <GradientButton
              className="w-full"
              htmlType="submit"
              buttonText="Login"
              loading={API?.isLoading}
            />
          </Form.Item>
        </Form>
      </div>
      {openfpmodal && (
        <ForgetPassword
          open={openfpmodal}
          close={() => setOpenfpmodal((pre) => !pre)}
        />
      )}
    </>
  );
};

export default LoginForm;
