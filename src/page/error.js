import { Result } from "antd";
import React from "react";
import GradientButton from "../component/common/GradientButton";

const Error = () => {
  return (
    <div className="w-full h-svh flex justify-center items-center">
      <div className="mobilescale">
        <Result
          status="404"
          title="404"
          subTitle="Sorry, you are not authorized to access this page."
          extra={
            <GradientButton
              className="textcolor"
              onClick={() => {
                window.location.assign("/");
              }}
              buttonText="Back Home"
            />
          }
        />
      </div>
    </div>
  );
};

export default Error;
