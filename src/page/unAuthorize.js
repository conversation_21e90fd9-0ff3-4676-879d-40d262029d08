import { Button, Result } from "antd";
import React from "react";

const Error = () => {
  return (
    <div className="w-full h-svh flex justify-center items-center">
      <div className="mobilescale">
        <Result
          status="403"
          title="403"
          subTitle="Sorry, you are not authorized to access this page."
          extra={
            <Button
              className="textcolor"
              type="primary"
              onClick={() => {
                window.location.assign("/");
              }}
            >
              Back Home
            </Button>
          }
        />
      </div>
    </div>
  );
};

export default Error;
